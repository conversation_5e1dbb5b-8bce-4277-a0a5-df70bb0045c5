
import React, { useState } from 'react';
import { toast } from 'sonner';
import { PlusCircle, Save, X, Edit, Trash2 } from 'lucide-react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import type { EditingItem } from '@/hooks/useSettings';

interface ListManagerProps {
  title: string;
  items: string[];
  icon: React.ReactNode;
  onSave: (items: string[]) => void;
  isLoading?: boolean;
}

export const ListManager = ({
  title,
  items,
  icon,
  onSave,
  isLoading = false
}: ListManagerProps) => {
  const [newItem, setNewItem] = useState('');
  const [editingItem, setEditingItem] = useState<EditingItem>(null);
  const [localItems, setLocalItems] = useState<string[]>(items);

  // Update localItems when items prop changes
  React.useEffect(() => {
    setLocalItems(items);
  }, [items]);

  const handleAdd = () => {
    if (newItem.trim() && !localItems.includes(newItem.trim())) {
      const updatedItems = [...localItems, newItem.trim()];
      setLocalItems(updatedItems);
      setNewItem('');
    } else if (localItems.includes(newItem.trim())) {
      toast.error('Este item já existe!');
    }
  };

  const handleUpdate = () => {
    if (!editingItem) return;
    
    if (editingItem.value.trim() && !localItems.some((item, idx) => item === editingItem.value.trim() && idx !== editingItem.index)) {
      const updatedItems = [...localItems];
      updatedItems[editingItem.index] = editingItem.value.trim();
      setLocalItems(updatedItems);
      setEditingItem(null);
    } else if (localItems.some((item, idx) => item === editingItem.value.trim() && idx !== editingItem.index)) {
      toast.error('Este item já existe!');
    }
  };

  const handleDelete = (index: number) => {
    const updatedItems = [...localItems];
    updatedItems.splice(index, 1);
    setLocalItems(updatedItems);
  };

  const handleSave = () => {
    onSave(localItems);
  };

  return (
    <div className="animate-fade-in space-y-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
        <div className="flex items-center gap-2">
          <Input
            type="text"
            placeholder="Novo item"
            className="max-w-xs"
            value={newItem}
            onChange={(e) => setNewItem(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleAdd()}
          />
          <Button
            onClick={handleAdd}
            className="flex items-center gap-1"
            variant="default"
            size="sm"
          >
            <PlusCircle className="h-4 w-4" />
            <span>Adicionar</span>
          </Button>
        </div>
      </div>
      
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {localItems.map((item, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-100 shadow-sm">
              {editingItem && editingItem.index === index ? (
                <Input
                  type="text"
                  className="flex-1"
                  value={editingItem.value}
                  onChange={(e) => setEditingItem({ ...editingItem, value: e.target.value })}
                  autoFocus
                  onKeyDown={(e) => e.key === 'Enter' && handleUpdate()}
                />
              ) : (
                <div className="flex items-center">
                  {icon}
                  <span className="text-gray-800 ml-2">{item}</span>
                </div>
              )}
              
              <div className="flex items-center gap-1">
                {editingItem && editingItem.index === index ? (
                  <>
                    <Button
                      onClick={handleUpdate}
                      className="px-1 py-1"
                      variant="ghost"
                      size="sm"
                      title="Salvar"
                    >
                      <Save className="h-4 w-4 text-green-600" />
                    </Button>
                    <Button
                      onClick={() => setEditingItem(null)}
                      className="px-1 py-1"
                      variant="ghost"
                      size="sm"
                      title="Cancelar"
                    >
                      <X className="h-4 w-4 text-gray-600" />
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      onClick={() => setEditingItem({ index, value: item })}
                      className="px-1 py-1"
                      variant="ghost"
                      size="sm"
                      title="Editar"
                    >
                      <Edit className="h-4 w-4 text-blue-600" />
                    </Button>
                    <Button
                      onClick={() => handleDelete(index)}
                      className="px-1 py-1"
                      variant="ghost"
                      size="sm"
                      title="Excluir"
                    >
                      <Trash2 className="h-4 w-4 text-red-600" />
                    </Button>
                  </>
                )}
              </div>
            </div>
          ))}
          
          {localItems.length === 0 && (
            <div className="col-span-2 py-6 text-center text-gray-500">
              Nenhum item adicionado. Use o botão acima para adicionar.
            </div>
          )}
        </div>
      </div>
      
      <div className="mt-4">
        <Button
          onClick={handleSave}
          disabled={isLoading || JSON.stringify(items) === JSON.stringify(localItems)}
          className="flex items-center gap-2"
        >
          <Save className="h-4 w-4" />
          <span>{isLoading ? 'Salvando...' : 'Salvar Alterações'}</span>
        </Button>
      </div>
    </div>
  );
};
