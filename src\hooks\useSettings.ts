
import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getConfiguracoes, updateConfiguracao, saveConfiguracoes } from '@/utils/supabase-helpers';

export type EditingItem = {
  index: number;
  value: string;
} | null;

export type ConfigurationType = {
  nome: string;
  valor: string;
};

export const useSettings = () => {
  const queryClient = useQueryClient();
  const [loading, setLoading] = useState(false);

  // Query to fetch all configurations
  const { data: configurations, isLoading: isLoadingConfigs } = useQuery({
    queryKey: ['configurations'],
    queryFn: getConfiguracoes
  });

  // Helper to get configuration value by name
  const getConfigValue = useCallback((name: string): string => {
    if (!configurations) return '';
    const config = configurations.find(c => c.nome === name);
    return config?.valor || '';
  }, [configurations]);

  // Helper to get array configuration value
  const getArrayConfig = useCallback((name: string): string[] => {
    const value = getConfigValue(name);
    return value ? value.split(',').filter(Boolean) : [];
  }, [getConfigValue]);

  // Helper to get payment period configuration
  const getPaymentPeriodConfig = useCallback(() => {
    const diaInicio = getConfigValue('dia_inicio_pagamento') || '28';
    const diaLimite = getConfigValue('data_limite_pagamento') || '10';
    return {
      diaInicio: parseInt(diaInicio),
      diaLimite: parseInt(diaLimite)
    };
  }, [getConfigValue]);

  // Mutation to save all configurations
  const saveMutation = useMutation({
    mutationFn: saveConfiguracoes,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['configurations'] });
      toast.success('Configurações salvas com sucesso!');
    },
    onError: (error) => {
      console.error('Error saving settings:', error);
      toast.error('Erro ao salvar configurações');
    },
    onSettled: () => {
      setLoading(false);
    }
  });

  // Helper to save multiple configurations
  const saveSettings = async (settings: ConfigurationType[]) => {
    setLoading(true);
    await saveMutation.mutateAsync(settings);
  };

  return {
    configurations,
    getConfigValue,
    getArrayConfig,
    getPaymentPeriodConfig,
    saveSettings,
    loading,
    isLoadingConfigs
  };
};
