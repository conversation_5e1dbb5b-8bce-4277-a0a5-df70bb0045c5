
import { FluxoCaixa } from '@/types';

export const useFluxoCaixaCalculations = (fluxoCaixa: FluxoCaixa[] | undefined, saldoTotal: number | undefined) => {
  const getMonthlyFlowData = () => {
    if (!fluxoCaixa || fluxoCaixa.length === 0) return [];
    
    const months = [...new Set(fluxoCaixa.map(item => {
      const date = new Date(item.data);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    }))].sort();
    
    const lastSixMonths = months.slice(-6);
    
    return lastSixMonths.map(monthYear => {
      const [year, month] = monthYear.split('-');
      const monthName = new Date(parseInt(year), parseInt(month) - 1).toLocaleString('pt-BR', { month: 'short' });
      
      const monthEntries = fluxoCaixa.filter(item => {
        const date = new Date(item.data);
        const itemYear = date.getFullYear().toString();
        const itemMonth = String(date.getMonth() + 1).padStart(2, '0');
        return `${itemYear}-${itemMonth}` === monthYear;
      });
      
      const entrada = monthEntries
        .filter(item => item.tipo === 'entrada')
        .reduce((sum, item) => sum + item.valor, 0);
        
      const saida = monthEntries
        .filter(item => item.tipo === 'saida')
        .reduce((sum, item) => sum + item.valor, 0);
      
      return {
        name: monthName.charAt(0).toUpperCase() + monthName.slice(1, 3),
        entrada,
        saida
      };
    });
  };

  const getMonthlyTrends = () => {
    const monthlyData = getMonthlyFlowData();
    
    if (monthlyData.length < 2) {
      return {
        entradaTrend: 0,
        saidaTrend: 0
      };
    }
    
    const currentMonth = monthlyData[monthlyData.length - 1];
    const prevMonth = monthlyData[monthlyData.length - 2];
    
    // Calcular tendência com limitação entre -100% e 100%
    let entradaTrend = 0;
    if (prevMonth.entrada !== 0) {
      const rawTrend = ((currentMonth.entrada - prevMonth.entrada) / prevMonth.entrada) * 100;
      entradaTrend = Math.max(-100, Math.min(100, Math.round(rawTrend)));
    } else if (currentMonth.entrada > 0) {
      entradaTrend = 100; // Máximo de 100% quando não havia entrada anterior
    }

    let saidaTrend = 0;
    if (prevMonth.saida !== 0) {
      const rawTrend = ((currentMonth.saida - prevMonth.saida) / prevMonth.saida) * 100;
      saidaTrend = Math.max(-100, Math.min(100, Math.round(rawTrend)));
    } else if (currentMonth.saida > 0) {
      saidaTrend = 100; // Máximo de 100% quando não havia saída anterior
    }
    
    return {
      entradaTrend,
      saidaTrend
    };
  };

  const getTotals = () => {
    if (!fluxoCaixa) return { totalIncome: 0, totalExpenses: 0, balance: 0 };

    const totalIncome = fluxoCaixa
      .filter(item => item.tipo === 'entrada')
      .reduce((sum, item) => sum + item.valor, 0);

    const totalExpenses = fluxoCaixa
      .filter(item => item.tipo === 'saida')
      .reduce((sum, item) => sum + item.valor, 0);

    // When using filtered data, calculate balance from filtered totals instead of saldoTotal
    // This ensures the balance card reflects the same filtered dataset
    const calculatedBalance = totalIncome - totalExpenses;

    return {
      totalIncome,
      totalExpenses,
      balance: calculatedBalance
    };
  };

  return {
    monthlyFlowData: getMonthlyFlowData(),
    trends: getMonthlyTrends(),
    totals: getTotals()
  };
};
