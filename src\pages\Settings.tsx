
import React, { useState, useEffect } from 'react';
import AdminLayout from '@/components/layout/admin/AdminLayout';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { useSettings } from '@/hooks/useSettings';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ListManager } from '@/components/settings/ListManager';
import CommitteeRolesManager from '@/components/settings/CommitteeRolesManager';
import { Package, FileText, BarChart3, Shield, Users, Calendar, Save } from 'lucide-react';

const Settings = () => {
  const {
    getConfigValue,
    getArrayConfig,
    saveSettings,
    loading,
    isLoadingConfigs
  } = useSettings();

  const [valorMultaAtraso, setValorMultaAtraso] = useState<number>(1000);
  const [diasAtrasoMulta, setDiasAtrasoMulta] = useState<number>(0);
  const [diaInicioPagamento, setDiaInicioPagamento] = useState<number>(28);
  const [nomeCondominio, setNomeCondominio] = useState<string>('Prédio Azul');
  const [enderecoCondominio, setEnderecoCondominio] = useState<string>('Cassenda, rua 15 da administração.');
  const [valorQuota, setValorQuota] = useState<number>(2000);
  const [diaLimitePagamento, setDiaLimitePagamento] = useState<number>(10);
  const [emailContato, setEmailContato] = useState<string>('<EMAIL>');
  const [telefoneContato, setTelefoneContato] = useState<string>('937072131');
  
  // Security settings
  const [senhaObrigatoria, setSenhaObrigatoria] = useState<boolean>(true);
  const [sessaoInatividade, setSessaoInatividade] = useState<number>(30);
  const [logAuditoria, setLogAuditoria] = useState<boolean>(true);

  // Original values to detect changes
  const [originalValues, setOriginalValues] = useState({
    valorMultaAtraso: 1000,
    diasAtrasoMulta: 0,
    diaInicioPagamento: 28,
    nomeCondominio: 'Prédio Azul',
    enderecoCondominio: 'Cassenda, rua 15 da administração.',
    valorQuota: 2000,
    diaLimitePagamento: 10,
    emailContato: '<EMAIL>',
    telefoneContato: '937072131'
  });

  useEffect(() => {
    if (!isLoadingConfigs) {
      const values = {
        valorMultaAtraso: parseInt(getConfigValue('valor_multa_atraso')) || 1000,
        diasAtrasoMulta: parseInt(getConfigValue('dias_atraso_multa')) || 0,
        diaInicioPagamento: parseInt(getConfigValue('dia_inicio_pagamento')) || 28,
        nomeCondominio: getConfigValue('condominio_nome') || 'Prédio Azul',
        enderecoCondominio: getConfigValue('condominio_endereco') || 'Cassenda, rua 15 da administração.',
        valorQuota: parseInt(getConfigValue('valor_quota')) || 2000,
        diaLimitePagamento: parseInt(getConfigValue('data_limite_pagamento')) || 10,
        emailContato: getConfigValue('condominio_email') || '<EMAIL>',
        telefoneContato: getConfigValue('condominio_telefone') || '937072131'
      };

      setValorMultaAtraso(values.valorMultaAtraso);
      setDiasAtrasoMulta(values.diasAtrasoMulta);
      setDiaInicioPagamento(values.diaInicioPagamento);
      setNomeCondominio(values.nomeCondominio);
      setEnderecoCondominio(values.enderecoCondominio);
      setValorQuota(values.valorQuota);
      setDiaLimitePagamento(values.diaLimitePagamento);
      setEmailContato(values.emailContato);
      setTelefoneContato(values.telefoneContato);

      // Store original values for change detection
      setOriginalValues(values);

      // Security settings
      setSenhaObrigatoria(getConfigValue('senha_obrigatoria') === 'true');
      setSessaoInatividade(parseInt(getConfigValue('sessao_inatividade')) || 30);
      setLogAuditoria(getConfigValue('log_auditoria') === 'true');
    }
  }, [getConfigValue, isLoadingConfigs]);

  // Function to detect if there are changes in general settings
  const hasGeneralChanges = () => {
    return (
      valorMultaAtraso !== originalValues.valorMultaAtraso ||
      diasAtrasoMulta !== originalValues.diasAtrasoMulta ||
      diaInicioPagamento !== originalValues.diaInicioPagamento ||
      nomeCondominio !== originalValues.nomeCondominio ||
      enderecoCondominio !== originalValues.enderecoCondominio ||
      valorQuota !== originalValues.valorQuota ||
      diaLimitePagamento !== originalValues.diaLimitePagamento ||
      emailContato !== originalValues.emailContato ||
      telefoneContato !== originalValues.telefoneContato
    );
  };

  const handleSaveSettings = async () => {
    const configsToSave = [
      { nome: 'valor_multa_atraso', valor: valorMultaAtraso.toString() },
      { nome: 'dias_atraso_multa', valor: diasAtrasoMulta.toString() },
      { nome: 'dia_inicio_pagamento', valor: diaInicioPagamento.toString() },
      { nome: 'condominio_nome', valor: nomeCondominio },
      { nome: 'condominio_endereco', valor: enderecoCondominio },
      { nome: 'valor_quota', valor: valorQuota.toString() },
      { nome: 'data_limite_pagamento', valor: diaLimitePagamento.toString() },
      { nome: 'condominio_email', valor: emailContato },
      { nome: 'condominio_telefone', valor: telefoneContato }
    ];

    try {
      await saveSettings(configsToSave);

      // Update original values after successful save
      setOriginalValues({
        valorMultaAtraso,
        diasAtrasoMulta,
        diaInicioPagamento,
        nomeCondominio,
        enderecoCondominio,
        valorQuota,
        diaLimitePagamento,
        emailContato,
        telefoneContato
      });

      toast.success('Configurações salvas com sucesso!');
    } catch (error) {
      toast.error('Erro ao salvar configurações.');
    }
  };

  const handleSaveSecuritySettings = async () => {
    const securityConfigs = [
      { nome: 'senha_obrigatoria', valor: senhaObrigatoria.toString() },
      { nome: 'sessao_inatividade', valor: sessaoInatividade.toString() },
      { nome: 'log_auditoria', valor: logAuditoria.toString() }
    ];

    try {
      await saveSettings(securityConfigs);
      toast.success('Configurações de segurança salvas com sucesso!');
    } catch (error) {
      toast.error('Erro ao salvar configurações de segurança.');
    }
  };

  const handleSaveCategories = async (categories: string[]) => {
    const config = { nome: 'categorias', valor: categories.join(',') };
    try {
      await saveSettings([config]);
      toast.success('Categorias salvas com sucesso!');
    } catch (error) {
      toast.error('Erro ao salvar categorias.');
    }
  };





  return (
    <AdminLayout
      title="Configurações"
      subtitle="Gerencie as configurações do sistema"
    >
      <div className="container mx-auto p-6">
        <Tabs defaultValue="geral" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="geral">Geral</TabsTrigger>
            <TabsTrigger value="categorias">Categorias</TabsTrigger>
            <TabsTrigger value="funcoes-comissao">Funções da Comissão</TabsTrigger>
            <TabsTrigger value="seguranca">Segurança</TabsTrigger>
          </TabsList>

          <TabsContent value="geral" className="space-y-6">
            <div className="space-y-6">
              <h2 className="text-2xl font-semibold">Configurações Gerais</h2>

              {/* Grupo 1: Nome do Condomínio e E-mail de Contato */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="nome-condominio">Nome do Condomínio</Label>
                  <Input
                    id="nome-condominio"
                    value={nomeCondominio}
                    onChange={(e) => setNomeCondominio(e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="email-contato">E-mail de Contato</Label>
                  <Input
                    id="email-contato"
                    type="email"
                    value={emailContato}
                    onChange={(e) => setEmailContato(e.target.value)}
                  />
                </div>
              </div>

              {/* Grupo 2: Endereço e Telefone de Contato */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="endereco">Endereço</Label>
                  <Input
                    id="endereco"
                    value={enderecoCondominio}
                    onChange={(e) => setEnderecoCondominio(e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="telefone-contato">Telefone de Contato</Label>
                  <Input
                    id="telefone-contato"
                    value={telefoneContato}
                    onChange={(e) => setTelefoneContato(e.target.value)}
                  />
                </div>
              </div>

              {/* Grupo 3: Valor da Quota e Valor da Multa por Atraso */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="valor-quota">Valor da Quota</Label>
                  <Input
                    id="valor-quota"
                    type="number"
                    value={valorQuota}
                    onChange={(e) => setValorQuota(Number(e.target.value))}
                  />
                </div>

                <div>
                  <Label htmlFor="valor-multa-atraso" className="flex items-center gap-2">
                    <span className="text-sm">ℹ️</span>
                    Valor da Multa por Atraso
                  </Label>
                  <Input
                    id="valor-multa-atraso"
                    type="number"
                    value={valorMultaAtraso}
                    onChange={(e) => setValorMultaAtraso(Number(e.target.value))}
                  />
                  <p className="text-sm text-muted-foreground mt-1">
                    Este valor será aplicado automaticamente às quotas registradas após o dia limite.
                  </p>
                </div>
              </div>

              <div className="border-t pt-6">
                {/* Grupo 4: Configurações de Período (3 colunas) */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <Label htmlFor="dia-inicio-periodo" className="flex items-center gap-2">
                      <span className="text-sm">📅</span>
                      Dia Início do Período
                    </Label>
                    <Select
                      value={diaInicioPagamento.toString()}
                      onValueChange={(value) => setDiaInicioPagamento(parseInt(value))}
                    >
                      <SelectTrigger className="mt-2">
                        <SelectValue placeholder="Selecione o dia" />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 31 }, (_, i) => i + 1).map((dia) => (
                          <SelectItem key={dia} value={dia.toString()}>
                            Dia {dia}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-sm text-muted-foreground mt-1">
                      Dia do mês em que inicia o período de cobrança das quotas.
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="dia-limite" className="flex items-center gap-2">
                      <span className="text-sm">📅</span>
                      Dia Limite de Pagamento da Quota
                    </Label>
                    <Select
                      value={diaLimitePagamento.toString()}
                      onValueChange={(value) => setDiaLimitePagamento(parseInt(value))}
                    >
                      <SelectTrigger className="mt-2">
                        <SelectValue placeholder="Selecione o dia" />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 31 }, (_, i) => i + 1).map((dia) => (
                          <SelectItem key={dia} value={dia.toString()}>
                            Dia {dia}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-sm text-muted-foreground mt-1">
                      Este dia será usado como padrão para a data limite de todas as quotas.
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="dias-tolerancia" className="flex items-center gap-2">
                      <span className="text-sm">📅</span>
                      Dias de Tolerância para Multa
                    </Label>
                    <Input
                      id="dias-tolerancia"
                      type="number"
                      value={diasAtrasoMulta}
                      onChange={(e) => setDiasAtrasoMulta(Number(e.target.value))}
                      className="mt-2"
                    />
                    <p className="text-sm text-muted-foreground mt-1">
                      Número de dias após o vencimento que não aplicará multa (0 = multa aplicada imediatamente).
                    </p>
                  </div>
                </div>
              </div>

              <Button
                onClick={handleSaveSettings}
                disabled={loading || isLoadingConfigs || !hasGeneralChanges()}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                <span>{loading ? 'Salvando...' : 'Salvar Alterações'}</span>
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="categorias" className="space-y-6">
            <ListManager
              title="Categorias do Fluxo de Caixa"
              items={getArrayConfig('categorias')}
              icon={<Package className="h-4 w-4 text-blue-600" />}
              onSave={handleSaveCategories}
              isLoading={loading}
            />
          </TabsContent>



          <TabsContent value="funcoes-comissao" className="space-y-6">
            <CommitteeRolesManager />
          </TabsContent>

          <TabsContent value="seguranca" className="space-y-6">
            <div className="space-y-6">
              <h2 className="text-2xl font-semibold">Configurações de Segurança</h2>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="senha-obrigatoria">Exigir Senha Forte</Label>
                    <p className="text-sm text-muted-foreground">
                      As senhas devem ter pelo menos 8 caracteres, incluindo letras maiúsculas, minúsculas, números e símbolos.
                    </p>
                  </div>
                  <Switch
                    id="senha-obrigatoria"
                    checked={senhaObrigatoria}
                    onCheckedChange={setSenhaObrigatoria}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sessao-inatividade">Período de Inatividade</Label>
                  <Select
                    value={sessaoInatividade.toString()}
                    onValueChange={(value) => setSessaoInatividade(parseInt(value))}
                  >
                    <SelectTrigger className="max-w-xs">
                      <SelectValue placeholder="Selecione o tempo" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="30">30 minutos</SelectItem>
                      <SelectItem value="60">1 hora</SelectItem>
                      <SelectItem value="120">2 horas</SelectItem>
                      <SelectItem value="240">4 horas</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">
                    Após esse período de inatividade, o usuário será automaticamente desconectado.
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tentativas-login">Tentativas de Login</Label>
                  <Select value="5">
                    <SelectTrigger className="max-w-xs">
                      <SelectValue placeholder="Selecione o número" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="3">3 tentativas</SelectItem>
                      <SelectItem value="5">5 tentativas</SelectItem>
                      <SelectItem value="10">10 tentativas</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">
                    Após exceder esse número de tentativas de login malsucedidas, a conta será temporariamente bloqueada.
                  </p>
                </div>

                <div className="space-y-2">
                  <Label>Permissões de Acesso</Label>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-sm font-medium">👤 Administradores</span>
                        <p className="text-sm text-muted-foreground">Acesso total</p>
                      </div>
                      <span className="text-sm text-green-600 font-medium">Acesso total</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-sm font-medium">🏠 Convidados</span>
                        <p className="text-sm text-muted-foreground">Os convidados podem visualizar dados, mas não podem criar, editar ou excluir registros.</p>
                      </div>
                      <span className="text-sm text-orange-600 font-medium">Acesso limitado</span>
                    </div>
                  </div>
                </div>
              </div>

              <Button
                onClick={handleSaveSecuritySettings}
                disabled={loading}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                <span>{loading ? 'Salvando...' : 'Salvar Configurações'}</span>
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default Settings;
