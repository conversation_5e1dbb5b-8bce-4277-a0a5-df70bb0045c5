
import { useState } from 'react';
import { FluxoCaixa } from '@/types';
import { useFluxoCaixaData } from './useFluxoCaixaData';
import { useFluxoCaixaFilters } from './useFluxoCaixaFilters';
import { useFluxoCaixaMutations } from './useFluxoCaixaMutations';
import { useFluxoCaixaCalculations } from './useFluxoCaixaCalculations';

export const useFluxoCaixa = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedFlow, setSelectedFlow] = useState<FluxoCaixa | null>(null);
  const [flowToDelete, setFlowToDelete] = useState<FluxoCaixa | null>(null);

  const { fluxoCaixa, saldoTotal, isLoading } = useFluxoCaixaData();
  const { filters, filteredFluxoCaixa, handleFilterChange, clearAllFilters, hasActiveFilters } = useFluxoCaixaFilters(fluxoCaixa);
  const { createMutation, updateMutation, deleteMutation, handleSaveLancamento } = useFluxoCaixaMutations();
  // Use filtered data for calculations to sync summary cards with active filters
  const { monthlyFlowData, trends, totals } = useFluxoCaixaCalculations(filteredFluxoCaixa, saldoTotal);

  const openEditModal = (flow: FluxoCaixa) => {
    setSelectedFlow(flow);
    setIsModalOpen(true);
  };

  const openCreateModal = () => {
    setSelectedFlow(null);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedFlow(null);
  };

  const handleDelete = (flow: FluxoCaixa) => {
    setFlowToDelete(flow);
  };

  const confirmDelete = () => {
    if (flowToDelete) {
      deleteMutation.mutate(flowToDelete.id);
      setFlowToDelete(null);
    }
  };

  const handleSave = async (data: any): Promise<void> => {
    try {
      const processedData = await handleSaveLancamento(data);
      
      if (selectedFlow) {
        console.log('📝 useFluxoCaixa: Updating existing flow:', selectedFlow.id);
        updateMutation.mutate({ 
          id: selectedFlow.id, 
          data: processedData
        });
      } else {
        console.log('➕ useFluxoCaixa: Creating new flow');
        createMutation.mutate(processedData as any);
      }
      
      setIsModalOpen(false);
    } catch (error) {
      console.error('❌ useFluxoCaixa: Error processing lancamento:', error);
    }
  };

  return {
    fluxoCaixa: filteredFluxoCaixa,
    isLoading,
    monthlyFlowData,
    totals,
    trends,
    filters,
    isModalOpen,
    selectedFlow,
    flowToDelete,
    handleFilterChange,
    clearAllFilters,
    hasActiveFilters,
    openEditModal,
    openCreateModal,
    closeModal,
    handleSaveLancamento: handleSave,
    handleDelete,
    confirmDelete,
    setFlowToDelete
  };
};
