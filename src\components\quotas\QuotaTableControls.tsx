
import React from 'react';
import { Info, Shield } from 'lucide-react';
import QuotaFilters from '@/components/filters/QuotaFilters';
import { Badge } from '@/components/ui/badge';
import { Morador } from '@/types';
import { useNavigate } from 'react-router-dom';

interface QuotaTableControlsProps {
  moradores?: Morador[];
  selectedMorador?: Morador | null;
  onMoradorChange?: (morador: Morador | null) => void;
  selectedStatus?: string | null;
  onStatusChange?: (status: string | null) => void;
  selectedFineStatus?: string | null;
  onFineStatusChange?: (status: string | null) => void;
  selectedMonth?: number | null;
  onMonthChange?: (month: number | null) => void;
  selectedYear?: number | null;
  onYearChange?: (year: number | null) => void;
}

export const QuotaTableControls: React.FC<QuotaTableControlsProps> = ({
  moradores = [],
  selectedMorador,
  onMoradorChange,
  selectedStatus,
  onStatusChange,
  selectedFineStatus,
  onFineStatusChange,
  selectedMonth,
  onMonthChange,
  selectedYear,
  onYearChange
}) => {
  const navigate = useNavigate();
  const exemptCount = moradores.filter(m => m.isento_quotas).length;

  const handleNavigateToExemptResidents = () => {
    // Navegar para a página de moradores com filtro de isentos ativo
    navigate('/residents?showExempt=true');
  };

  return (
    <div className="space-y-3">
      {/* Info sobre isenções */}
      {exemptCount > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
          <div className="flex items-center gap-2 text-yellow-800">
            <Info className="w-4 h-4" />
            <span className="text-sm font-medium">Informação sobre Isenções</span>
          </div>
          <div className="mt-1 text-sm text-yellow-700">
            <Badge
              variant="secondary"
              className="bg-yellow-100 text-yellow-800 mr-2 cursor-pointer hover:bg-yellow-200 transition-colors"
              onClick={handleNavigateToExemptResidents}
              title="Clique para ver moradores isentos"
            >
              <Shield className="w-3 h-3 mr-1" />
              {exemptCount} moradores isentos
            </Badge>
            não terão quotas geradas automaticamente. Use a opção "Incluir Isentos" no gerador se necessário.
          </div>
        </div>
      )}

      {/* Filters and Actions - Unified Layout */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        {/* Filters Section */}
        {moradores.length > 0 && onMoradorChange && onStatusChange && onFineStatusChange && onMonthChange && onYearChange && (
          <QuotaFilters
            moradores={moradores}
            selectedMorador={selectedMorador || null}
            onMoradorChange={onMoradorChange}
            selectedStatus={selectedStatus || null}
            onStatusChange={onStatusChange}
            selectedFineStatus={selectedFineStatus || null}
            onFineStatusChange={onFineStatusChange}
            selectedMonth={selectedMonth || null}
            onMonthChange={onMonthChange}
            selectedYear={selectedYear || null}
            onYearChange={onYearChange}
            className="flex-1"
          />
        )}
      </div>
    </div>
  );
};
