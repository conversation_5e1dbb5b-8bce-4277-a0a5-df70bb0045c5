
/**
 * Componente Header - Cabeçalho Principal do Sistema
 *
 * Cabeçalho fixo que contém logo, navegação, notificações e perfil do usuário.
 * Inclui funcionalidades de logout, perfil e controle da sidebar.
 *
 * <AUTHOR> Prédio Azul
 * @version 1.0
 */

import React, { useState } from 'react';
import { Bell, User, Grid3X3, Menu, LogOut } from 'lucide-react';
import { useNotifications } from '@/hooks/useNotifications';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import { useSidebar } from '@/contexts/SidebarContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import UserProfileModal from '@/components/modals/UserProfileModal';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

/**
 * Interface para as propriedades do componente Header
 */
interface HeaderProps {
  title: string;                    // Título da página atual
  subtitle?: string;                // Subtítulo opcional da página
  className?: string;               // Classes CSS adicionais
  actions?: React.ReactNode;        // Ações personalizadas para a página
}

/**
 * Componente funcional Header
 *
 * Renderiza o cabeçalho principal com navegação, perfil e controles.
 * Gerencia estado do perfil do usuário e funcionalidades de logout.
 *
 * @param props - Propriedades do componente
 * @returns {JSX.Element} Cabeçalho renderizado
 */
const Header: React.FC<HeaderProps> = ({ title, subtitle, className, actions }) => {
  // Hooks de contexto para autenticação e sidebar
  const { user, session, signOut } = useAuth();
  const { collapsed, toggleCollapsed } = useSidebar();

  // Estados locais para modal e dados do usuário
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const [userName, setUserName] = useState<string | null>(null);
  const [userPhoto, setUserPhoto] = useState<string | null>(null);
  const { toast } = useToast();

  // Hook de notificações
  const {
    unreadCount,
    markAllAsRead,
    getRecentNotifications,
    markAsRead
  } = useNotifications();

  // Função para formatar tempo relativo
  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'agora';
    if (diffInMinutes < 60) return `há ${diffInMinutes} minuto${diffInMinutes > 1 ? 's' : ''}`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `há ${diffInHours} hora${diffInHours > 1 ? 's' : ''}`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays === 1) return 'ontem';
    if (diffInDays < 7) return `há ${diffInDays} dias`;

    return date.toLocaleDateString('pt-BR');
  };

  // Função para obter cor do indicador por tipo
  const getNotificationColor = (type: string, isRead: boolean) => {
    if (isRead) return 'bg-gray-400';

    switch (type) {
      case 'quota': return 'bg-blue-500';
      case 'payment': return 'bg-green-500';
      case 'document': return 'bg-purple-500';
      default: return 'bg-orange-500';
    }
  };

  // Efeito para buscar perfil do usuário quando logado
  React.useEffect(() => {
    if (user?.id) {
      fetchUserProfile();
    }
  }, [user?.id]);

  // Efeito para fechar dropdown de notificações ao clicar fora
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (isNotificationsOpen && !target.closest('.notifications-dropdown')) {
        setIsNotificationsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isNotificationsOpen]);

  /**
   * Busca dados do perfil do usuário no Supabase
   * Atualiza nome e foto do usuário no estado local
   */
  const fetchUserProfile = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('name, photo')
        .eq('id', user?.id)
        .single();

      if (error) {
        console.error('Error fetching user profile:', error);
        return;
      }

      if (data) {
        setUserName(data.name);
        setUserPhoto(data.photo);
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
    }
  };

  /**
   * Callback para atualizar perfil após edição
   * Recarrega dados do perfil do usuário
   */
  const handleUpdateProfile = () => {
    fetchUserProfile();
  };

  /**
   * Função para realizar logout do usuário
   * Utiliza o método signOut do contexto de autenticação
   */
  const handleLogout = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  return (
    <>
      {/* Main Header - Fixed at top */}
      <header className={cn("fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm", className)}>
        <div className="flex items-center justify-between h-14 px-4">
          {/* Left Section - Logo, System Name and Menu */}
          <div className="flex items-center space-x-3">
            {/* Logo */}
            <div className="flex items-center justify-center w-8 h-8 bg-primary rounded-lg">
              <Grid3X3 size={16} className="text-white" />
            </div>

            {/* System Name */}
            <span className="text-lg font-bold text-primary">Prédio Azul</span>

            {/* Menu Button */}
            <button
              onClick={toggleCollapsed}
              className="p-1.5 rounded-md hover:bg-gray-100 transition-colors"
            >
              <Menu size={16} className="text-gray-600" />
            </button>
          </div>

          {/* Right Section - Actions and Profile */}
          <div className="flex items-center space-x-2">
            {/* Notifications */}
            <div className="relative notifications-dropdown">
              <button
                className="relative p-1.5 rounded-md hover:bg-gray-100 transition-colors"
                onClick={() => setIsNotificationsOpen(!isNotificationsOpen)}
              >
                <Bell size={16} className="text-gray-600" />
                {unreadCount > 0 && (
                  <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium">
                    {unreadCount > 9 ? '9+' : unreadCount}
                  </span>
                )}
              </button>

              {/* Notifications Dropdown */}
              {isNotificationsOpen && (
                <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                  <div className="p-4 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-semibold text-gray-800">
                        Notificações {unreadCount > 0 && `(${unreadCount})`}
                      </h3>
                      {unreadCount > 0 && (
                        <button
                          className="text-xs text-blue-600 hover:text-blue-800 transition-colors"
                          onClick={() => {
                            markAllAsRead();
                            toast({
                              title: 'Notificações marcadas como lidas',
                              description: `${unreadCount} notificação${unreadCount > 1 ? 'ões foram marcadas' : ' foi marcada'} como lida${unreadCount > 1 ? 's' : ''}.`
                            });
                          }}
                        >
                          Marcar todas como lidas
                        </button>
                      )}
                    </div>
                  </div>

                  <div className="max-h-96 overflow-y-auto">
                    {getRecentNotifications(10).length > 0 ? (
                      getRecentNotifications(10).map((notification, index) => (
                        <div
                          key={notification.id}
                          className={`p-3 hover:bg-gray-50 cursor-pointer transition-colors ${
                            index < getRecentNotifications(10).length - 1 ? 'border-b border-gray-100' : ''
                          }`}
                          onClick={() => {
                            if (!notification.isRead) {
                              markAsRead(notification.id);
                            }
                          }}
                        >
                          <div className="flex items-start space-x-3">
                            <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${getNotificationColor(notification.type, notification.isRead)}`}></div>
                            <div className="flex-1 min-w-0">
                              <p className={`text-sm ${notification.isRead ? 'text-gray-700' : 'font-medium text-gray-800'}`}>
                                {notification.title}
                              </p>
                              <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                                {notification.description}
                              </p>
                              <p className="text-xs text-gray-500 mt-1">
                                {formatTimeAgo(notification.timestamp)}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="p-6 text-center text-gray-500">
                        <Bell className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                        <p className="text-sm">Nenhuma notificação</p>
                      </div>
                    )}
                  </div>

                  <div className="p-3 border-t border-gray-200">
                    <button
                      className="w-full text-center text-xs text-blue-600 hover:text-blue-800 transition-colors"
                      onClick={() => {
                        setIsNotificationsOpen(false);
                        // TODO: Navegar para página de notificações ou abrir modal
                        toast({
                          title: 'Página de notificações',
                          description: 'Funcionalidade em desenvolvimento. Em breve você poderá ver todas as notificações em uma página dedicada.'
                        });
                      }}
                    >
                      Ver todas as notificações
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* User Profile */}
            <div
              className="flex items-center space-x-2 cursor-pointer p-1.5 rounded-md hover:bg-gray-50 transition-colors"
              onClick={() => setIsProfileModalOpen(true)}
            >
              <Avatar className="h-7 w-7 border border-gray-200">
                <AvatarImage src={userPhoto || undefined} alt={userName || 'User'} />
                <AvatarFallback>
                  <User className="h-4 w-4 text-gray-400" />
                </AvatarFallback>
              </Avatar>
              <div className="hidden md:block">
                <p className="text-sm font-medium text-gray-700">{userName || 'Usuário'}</p>
              </div>
            </div>

            {/* Logout Button */}
            <button
              onClick={handleLogout}
              className="p-1.5 rounded-md hover:bg-gray-100 hover:text-red-600 transition-colors"
              title="Sair"
            >
              <LogOut size={16} className="text-gray-600" />
            </button>
          </div>
        </div>
      </header>

      {/* Page Title Section - Below fixed header */}
      <div className="mt-14 bg-gray-50 py-4 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">{title}</h1>
            {subtitle && <p className="text-sm text-gray-500 mt-1">{subtitle}</p>}
          </div>
          {actions && (
            <div className="flex-shrink-0">
              {actions}
            </div>
          )}
        </div>
      </div>

      {/* User Profile Modal */}
      <UserProfileModal
        isOpen={isProfileModalOpen}
        onClose={() => setIsProfileModalOpen(false)}
        onProfileUpdate={handleUpdateProfile}
      />
    </>
  );
};

export default Header;
