
export const generateCredentialsPDF = async (credentials: { name: string; email: string; password: string; apartment: string }): Promise<void> => {
  try {
    // Importar jsPDF dinamicamente
    const { default: jsPDF } = await import('jspdf');

    // Criar documento PDF
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;

    // Cores do tema Prédio Azul
    const primaryColor = '#0066B3';
    const textColor = '#374151';
    const lightGray = '#F3F4F6';

    // Função para carregar logo como base64
    const loadImageAsBase64 = (src: string): Promise<string> => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.crossOrigin = 'anonymous';
        img.onload = () => {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          if (!ctx) {
            reject(new Error('Não foi possível criar contexto do canvas'));
            return;
          }
          canvas.width = img.width;
          canvas.height = img.height;
          ctx.drawImage(img, 0, 0);
          resolve(canvas.toDataURL('image/jpeg', 0.8));
        };
        img.onerror = () => reject(new Error('Erro ao carregar imagem'));
        img.src = src;
      });
    };

    // Adicionar marca d'água
    try {
      const logoBase64 = await loadImageAsBase64('/Predio Azul Logo.jpg');
      if (logoBase64) {
        // Marca d'água (transparente e centralizada)
        doc.saveGraphicsState();
        doc.setGState(new (doc as any).GState({ opacity: 0.1 }));
        doc.addImage(
          logoBase64,
          'JPEG',
          pageWidth / 2 - 40,
          pageHeight / 2 - 30,
          80,
          60
        );
        doc.restoreGraphicsState();

        // Logo no cabeçalho
        doc.addImage(logoBase64, 'JPEG', 15, 15, 30, 20);
      }
    } catch (error) {
      console.warn('Não foi possível carregar o logo:', error);
    }

    // Título principal
    doc.setFontSize(18);
    doc.setTextColor(primaryColor);
    doc.setFont('helvetica', 'bold');
    doc.text('CREDENCIAIS DE ACESSO', pageWidth / 2, 25, { align: 'center' });

    // Subtítulo
    doc.setFontSize(14);
    doc.setTextColor(textColor);
    doc.setFont('helvetica', 'normal');
    doc.text('Condomínio Prédio Azul', pageWidth / 2, 32, { align: 'center' });

    // Data de geração
    doc.setFontSize(10);
    doc.setTextColor('#6B7280');
    doc.text(
      `Gerado em: ${new Date().toLocaleDateString('pt-PT')}`,
      pageWidth - 15,
      15,
      { align: 'right' }
    );

    // Linha separadora
    doc.setDrawColor(primaryColor);
    doc.setLineWidth(0.5);
    doc.line(15, 40, pageWidth - 15, 40);

    // Dados das credenciais
    let yPosition = 60;

    // Caixa de destaque para as credenciais
    doc.setFillColor(lightGray);
    doc.rect(15, yPosition - 5, pageWidth - 30, 60, 'F');

    // Borda da caixa
    doc.setDrawColor(primaryColor);
    doc.setLineWidth(0.3);
    doc.rect(15, yPosition - 5, pageWidth - 30, 60);

    // Título da seção
    doc.setFontSize(14);
    doc.setTextColor(primaryColor);
    doc.setFont('helvetica', 'bold');
    doc.text('DADOS DE ACESSO:', 20, yPosition + 5);

    yPosition += 15;

    // Dados das credenciais
    doc.setFontSize(12);
    doc.setTextColor(textColor);
    doc.setFont('helvetica', 'normal');

    const credentialsData = [
      ['Nome:', credentials.name],
      ['Apartamento:', credentials.apartment],
      ['Email:', credentials.email],
      ['Senha:', credentials.password]
    ];

    credentialsData.forEach(([label, value]) => {
      doc.setFont('helvetica', 'bold');
      doc.text(label, 25, yPosition);
      doc.setFont('helvetica', 'normal');
      doc.text(value, 60, yPosition);
      yPosition += 8;
    });

    // Instruções importantes
    yPosition += 15;
    doc.setFontSize(14);
    doc.setTextColor(primaryColor);
    doc.setFont('helvetica', 'bold');
    doc.text('INSTRUÇÕES IMPORTANTES:', 15, yPosition);

    yPosition += 10;
    doc.setFontSize(11);
    doc.setTextColor(textColor);
    doc.setFont('helvetica', 'normal');

    const instructions = [
      '• Esta é sua senha temporária',
      '• Você será solicitado a alterá-la no primeiro acesso',
      '• Mantenha suas credenciais em segurança',
      '• Em caso de dúvidas, contacte a administração'
    ];

    instructions.forEach(instruction => {
      doc.text(instruction, 20, yPosition);
      yPosition += 7;
    });

    // Rodapé
    const footerY = pageHeight - 25;
    doc.setDrawColor(lightGray);
    doc.setLineWidth(0.3);
    doc.line(15, footerY, pageWidth - 15, footerY);

    doc.setFontSize(8);
    doc.setTextColor('#6B7280');
    doc.setFont('helvetica', 'normal');
    doc.text('Desenvolvido por CC', 15, footerY + 10);
    doc.text('Condomínio Prédio Azul', pageWidth - 15, footerY + 10, { align: 'right' });

    // Gerar nome do arquivo
    const timestamp = new Date().toISOString().slice(0, 10);
    const filename = `credenciais_${credentials.apartment}_${credentials.name.replace(/\s+/g, '_')}_${timestamp}.pdf`;

    // Salvar PDF
    doc.save(filename);

  } catch (error) {
    console.error('Erro ao gerar PDF de credenciais:', error);
    throw new Error('Erro ao gerar PDF de credenciais');
  }
};
