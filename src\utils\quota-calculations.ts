
import { format, differenceInDays, parseISO, isAfter, isBefore, isEqual, addMonths, subMonths } from 'date-fns';

export interface MultaConfig {
  valorMulta: number;
  diasAtrasoMulta: number;
  diaInicioPagamento?: number;
  diaLimitePagamento?: number;
}

// Função para calcular a data de início do período de pagamento
export const calculatePaymentPeriodStart = (
  mes: number,
  ano: number,
  diaInicio: number = 28
): Date => {
  // O período de pagamento inicia no dia configurado do mês anterior
  const mesAnterior = mes === 1 ? 12 : mes - 1;
  const anoAnterior = mes === 1 ? ano - 1 : ano;
  
  return new Date(anoAnterior, mesAnterior - 1, diaInicio);
};

// Função para calcular a data limite do período de pagamento
export const calculatePaymentPeriodEnd = (
  mes: number,
  ano: number,
  diaLimite: number = 10
): Date => {
  return new Date(ano, mes - 1, diaLimite);
};

// Função para verificar se uma data está dentro do período de pagamento
export const isWithinPaymentPeriod = (
  dataPagamento: Date,
  mes: number,
  ano: number,
  diaInicio: number = 28,
  diaLimite: number = 10
): boolean => {
  const inicioPeríodo = calculatePaymentPeriodStart(mes, ano, diaInicio);
  const fimPeríodo = calculatePaymentPeriodEnd(mes, ano, diaLimite);
  
  return dataPagamento >= inicioPeríodo && dataPagamento <= fimPeríodo;
};

export const calculateFineAmount = (
  dataPagamento: string | null,
  dataVencimento: string,
  config: MultaConfig,
  mes?: number,
  ano?: number
): number => {
  console.log('🎯 Calculando multa com período configurável:', {
    dataPagamento,
    dataVencimento,
    config,
    mes,
    ano
  });

  const vencimento = parseISO(dataVencimento);
  const hoje = new Date();
  
  // Usar configurações do período ou valores padrão
  const diaInicio = config.diaInicioPagamento || 28;
  const diaLimite = config.diaLimitePagamento || 10;
  
  // Se a quota foi paga
  if (dataPagamento) {
    const pagamento = parseISO(dataPagamento);
    
    // Se temos mês e ano, verificar se o pagamento foi dentro do período configurado
    if (mes && ano) {
      const dentroDoPeríodo = isWithinPaymentPeriod(pagamento, mes, ano, diaInicio, diaLimite);
      
      if (dentroDoPeríodo) {
        console.log('✅ Sem multa - pagamento dentro do período configurado');
        return 0;
      }
      
      // Se não está dentro do período, calcular multa baseada nos dias de tolerância
      const fimPeríodo = calculatePaymentPeriodEnd(mes, ano, diaLimite);
      const diffDays = differenceInDays(pagamento, fimPeríodo);
      
      console.log('📅 Diferença em dias após fim do período:', diffDays);
      console.log('📋 Dias tolerância configurados:', config.diasAtrasoMulta);
      
      if (diffDays > config.diasAtrasoMulta) {
        console.log('💰 Aplicando multa por pagamento após período + tolerância:', config.valorMulta, 'Kz');
        return config.valorMulta;
      }
      
      console.log('✅ Sem multa - pagamento dentro do prazo de tolerância');
      return 0;
    }
    
    // Fallback para lógica antiga quando não temos mês/ano
    const diffDays = differenceInDays(pagamento, vencimento);
    
    console.log('📅 Diferença em dias (pagamento vs vencimento):', diffDays);
    console.log('📋 Dias tolerância configurados:', config.diasAtrasoMulta);
    
    if (diffDays > config.diasAtrasoMulta) {
      console.log('💰 Aplicando multa por pagamento em atraso:', config.valorMulta, 'Kz');
      return config.valorMulta;
    }
    
    console.log('✅ Sem multa - pagamento dentro do prazo');
    return 0;
  }
  
  // Se a quota NÃO foi paga, verificar se já está em atraso
  if (mes && ano) {
    const fimPeríodo = calculatePaymentPeriodEnd(mes, ano, diaLimite);
    const diffDaysFromToday = differenceInDays(hoje, fimPeríodo);
    
    console.log('📅 Diferença em dias (hoje vs fim do período):', diffDaysFromToday);
    
    if (diffDaysFromToday > config.diasAtrasoMulta) {
      console.log('💰 Aplicando multa por atraso após período + tolerância:', config.valorMulta, 'Kz');
      return config.valorMulta;
    }
    
    console.log('✅ Sem multa - ainda dentro do prazo de tolerância');
    return 0;
  }
  
  // Fallback para lógica antiga
  const diffDaysFromToday = differenceInDays(hoje, vencimento);
  
  console.log('📅 Diferença em dias (hoje vs vencimento):', diffDaysFromToday);
  
  if (diffDaysFromToday > config.diasAtrasoMulta) {
    console.log('💰 Aplicando multa por atraso:', config.valorMulta, 'Kz');
    return config.valorMulta;
  }
  
  console.log('✅ Sem multa - ainda dentro do prazo');
  return 0;
};

export const calculateQuotaStatus = (
  dataPagamento: string | null,
  dataVencimento: string
): string => {
  if (!dataPagamento) {
    const hoje = new Date();
    const vencimento = parseISO(dataVencimento);
    return isAfter(hoje, vencimento) ? 'Não Pago' : 'Não Pago';
  }
  return 'Pago';
};

export const calculateSituacaoMulta = (
  multa: number,
  dataPagamento: string | null,
  dataVencimento: string
): string => {
  console.log('🎯 Calculando situação da multa:', { multa, dataPagamento, dataVencimento });
  
  // Se não há multa, está regularizada
  if (multa === 0) {
    console.log('✅ Regularizada - sem multa');
    return 'Regularizada';
  }
  
  // Se há multa mas a quota foi paga, a situação da multa depende de se a multa foi regularizada
  // Para este cálculo automático, assumimos que se há multa > 0, não está regularizada
  if (multa > 0) {
    console.log('❌ Não Regularizada - há multa pendente');
    return 'Não Regularizada';
  }
  
  return 'Regularizada';
};

// Validação inteligente de multa baseada no período configurável
export const shouldApplyFine = (
  dataVencimento: string,
  config: MultaConfig,
  dataPagamento?: string | null,
  mes?: number,
  ano?: number
): boolean => {
  const hoje = new Date();
  
  // Usar configurações do período ou valores padrão
  const diaInicio = config.diaInicioPagamento || 28;
  const diaLimite = config.diaLimitePagamento || 10;
  
  // Se tem data de pagamento, verificar se pagou dentro do período
  if (dataPagamento && mes && ano) {
    const pagamento = parseISO(dataPagamento);
    const dentroDoPeríodo = isWithinPaymentPeriod(pagamento, mes, ano, diaInicio, diaLimite);
    
    if (dentroDoPeríodo) {
      return false; // Sem multa se pagou dentro do período
    }
    
    // Se pagou fora do período, verificar tolerância
    const fimPeríodo = calculatePaymentPeriodEnd(mes, ano, diaLimite);
    const diffDays = differenceInDays(pagamento, fimPeríodo);
    return diffDays > config.diasAtrasoMulta;
  }
  
  // Se tem data de pagamento mas não tem mes/ano, usar lógica antiga
  if (dataPagamento) {
    const pagamento = parseISO(dataPagamento);
    const vencimento = parseISO(dataVencimento);
    const diffDays = differenceInDays(pagamento, vencimento);
    return diffDays > config.diasAtrasoMulta;
  }
  
  // Se não foi pago, verificar se já está em atraso considerando o período
  if (mes && ano) {
    const fimPeríodo = calculatePaymentPeriodEnd(mes, ano, diaLimite);
    const diffDaysFromToday = differenceInDays(hoje, fimPeríodo);
    return diffDaysFromToday > config.diasAtrasoMulta;
  }
  
  // Fallback para lógica antiga
  const vencimento = parseISO(dataVencimento);
  const diffDaysFromToday = differenceInDays(hoje, vencimento);
  return diffDaysFromToday > config.diasAtrasoMulta;
};

// Correção automática de multa ao marcar como pago
export const correctFineOnPayment = (
  dataPagamento: string,
  dataVencimento: string,
  config: MultaConfig,
  mes?: number,
  ano?: number
): { shouldHaveFine: boolean; newFineAmount: number } => {
  const pagamento = parseISO(dataPagamento);
  
  // Usar período configurável se disponível
  if (mes && ano) {
    const diaInicio = config.diaInicioPagamento || 28;
    const diaLimite = config.diaLimitePagamento || 10;
    
    const dentroDoPeríodo = isWithinPaymentPeriod(pagamento, mes, ano, diaInicio, diaLimite);
    
    if (dentroDoPeríodo) {
      console.log('🔧 Correção automática: pagamento dentro do período configurado');
      return { shouldHaveFine: false, newFineAmount: 0 };
    }
    
    const fimPeríodo = calculatePaymentPeriodEnd(mes, ano, diaLimite);
    const diffDays = differenceInDays(pagamento, fimPeríodo);
    const shouldHaveFine = diffDays > config.diasAtrasoMulta;
    const newFineAmount = shouldHaveFine ? config.valorMulta : 0;
    
    console.log('🔧 Correção automática com período configurável:', {
      dataPagamento,
      fimPeríodo: format(fimPeríodo, 'yyyy-MM-dd'),
      diffDays,
      shouldHaveFine,
      newFineAmount
    });
    
    return { shouldHaveFine, newFineAmount };
  }
  
  // Fallback para lógica antiga
  const vencimento = parseISO(dataVencimento);
  const diffDays = differenceInDays(pagamento, vencimento);
  const shouldHaveFine = diffDays > config.diasAtrasoMulta;
  const newFineAmount = shouldHaveFine ? config.valorMulta : 0;
  
  console.log('🔧 Correção automática de multa:', {
    dataPagamento,
    dataVencimento,
    diffDays,
    shouldHaveFine,
    newFineAmount
  });
  
  return { shouldHaveFine, newFineAmount };
};



// Nova função para validar se uma quota pode ser criada
export const validateQuotaCreation = (
  mes: number,
  ano: number,
  dataVencimento?: string
): { isValid: boolean; message?: string } => {
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth() + 1;
  const currentYear = currentDate.getFullYear();

  // Validar se o mês/ano não é muito futuro
  if (ano > currentYear + 1 || (ano === currentYear + 1 && mes > currentMonth)) {
    return {
      isValid: false,
      message: 'Não é possível criar quotas muito distantes no futuro'
    };
  }

  // Validar se o mês/ano não é muito antigo
  if (ano < 2020) {
    return {
      isValid: false,
      message: 'Ano muito antigo para criação de quotas'
    };
  }

  return { isValid: true };
};

// Interface para quota simplificada para validação
interface QuotaForValidation {
  id: string;
  morador_id: string;
  mes: number;
  ano: number;
  status: string;
  multa: number;
  situacao?: string;
  isento_quotas?: boolean;
}

// Função para validar pagamento sequencial
export const validateSequentialPayment = (
  quotaToPayId: string,
  allQuotas: QuotaForValidation[]
): { isValid: boolean; message?: string; unpaidQuotas?: QuotaForValidation[] } => {
  console.log('🔍 Validando pagamento sequencial para quota:', quotaToPayId);

  // Encontrar a quota que se quer pagar
  const quotaToPay = allQuotas.find(q => q.id === quotaToPayId);
  if (!quotaToPay) {
    return {
      isValid: false,
      message: 'Quota não encontrada'
    };
  }

  console.log('📋 Quota a ser paga:', {
    morador_id: quotaToPay.morador_id,
    mes: quotaToPay.mes,
    ano: quotaToPay.ano,
    status: quotaToPay.status
  });

  // Se a quota já está paga, não precisa validar
  if (quotaToPay.status === 'Pago') {
    return {
      isValid: false,
      message: 'Esta quota já foi paga'
    };
  }

  // Filtrar quotas do mesmo morador que não estão pagas
  const moradorQuotas = allQuotas.filter(q =>
    q.morador_id === quotaToPay.morador_id &&
    q.status !== 'Pago'
  );

  console.log(`👤 Quotas não pagas do morador: ${moradorQuotas.length}`);

  // Ordenar por ano e mês (mais antigas primeiro)
  const sortedQuotas = moradorQuotas.sort((a, b) => {
    if (a.ano !== b.ano) {
      return a.ano - b.ano;
    }
    return a.mes - b.mes;
  });

  // Encontrar quotas anteriores não pagas
  const unpaidPreviousQuotas = sortedQuotas.filter(q => {
    // Quota é anterior se:
    // 1. Ano é menor, ou
    // 2. Mesmo ano mas mês é menor
    return (q.ano < quotaToPay.ano) ||
           (q.ano === quotaToPay.ano && q.mes < quotaToPay.mes);
  });

  console.log(`📅 Quotas anteriores não pagas: ${unpaidPreviousQuotas.length}`);

  if (unpaidPreviousQuotas.length > 0) {
    // Criar mensagem detalhada com os meses pendentes
    const monthNames = [
      'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
      'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
    ];

    const pendingPeriods = unpaidPreviousQuotas.map(q =>
      `${monthNames[q.mes - 1]}/${q.ano}`
    ).join(', ');

    const message = unpaidPreviousQuotas.length === 1
      ? `Você deve pagar primeiro a quota de ${pendingPeriods} antes de pagar esta.`
      : `Você deve pagar primeiro as quotas de ${pendingPeriods} antes de pagar esta.`;

    console.log('❌ Pagamento sequencial violado:', message);

    return {
      isValid: false,
      message,
      unpaidQuotas: unpaidPreviousQuotas
    };
  }

  console.log('✅ Pagamento sequencial válido');
  return { isValid: true };
};

// Função para validar regularização sequencial de multas
export const validateSequentialFineRegularization = (
  quotaToRegularizeId: string,
  allQuotas: QuotaForValidation[]
): { isValid: boolean; message?: string; unpaidFines?: QuotaForValidation[] } => {
  console.log('🔍 Validando regularização sequencial de multa para quota:', quotaToRegularizeId);

  // Encontrar a quota que se quer regularizar
  const quotaToRegularize = allQuotas.find(q => q.id === quotaToRegularizeId);
  if (!quotaToRegularize) {
    return {
      isValid: false,
      message: 'Quota não encontrada'
    };
  }

  console.log('📋 Quota a ser regularizada:', {
    morador_id: quotaToRegularize.morador_id,
    mes: quotaToRegularize.mes,
    ano: quotaToRegularize.ano,
    multa: quotaToRegularize.multa
  });

  // Se a quota não tem multa, não precisa regularizar
  if (!quotaToRegularize.multa || quotaToRegularize.multa <= 0) {
    return {
      isValid: false,
      message: 'Esta quota não possui multa para regularizar'
    };
  }

  // Filtrar quotas do mesmo morador que têm multa não regularizada
  const moradorQuotasWithFines = allQuotas.filter(q =>
    q.morador_id === quotaToRegularize.morador_id &&
    q.multa > 0 &&
    q.situacao !== 'Regularizada' // Verificar se a multa não foi regularizada
  );

  console.log(`👤 Quotas com multa do morador: ${moradorQuotasWithFines.length}`);

  // Ordenar por ano e mês (mais antigas primeiro)
  const sortedQuotasWithFines = moradorQuotasWithFines.sort((a, b) => {
    if (a.ano !== b.ano) {
      return a.ano - b.ano;
    }
    return a.mes - b.mes;
  });

  // Encontrar multas anteriores não regularizadas
  const unpaidPreviousFines = sortedQuotasWithFines.filter(q => {
    // Multa é anterior se:
    // 1. Ano é menor, ou
    // 2. Mesmo ano mas mês é menor
    return (q.ano < quotaToRegularize.ano) ||
           (q.ano === quotaToRegularize.ano && q.mes < quotaToRegularize.mes);
  });

  console.log(`📅 Multas anteriores não regularizadas: ${unpaidPreviousFines.length}`);

  if (unpaidPreviousFines.length > 0) {
    // Criar mensagem detalhada com os meses pendentes
    const monthNames = [
      'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
      'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
    ];

    const pendingPeriods = unpaidPreviousFines.map(q =>
      `${monthNames[q.mes - 1]}/${q.ano}`
    ).join(', ');

    const message = unpaidPreviousFines.length === 1
      ? `Você deve regularizar primeiro a multa de ${pendingPeriods} antes de regularizar esta.`
      : `Você deve regularizar primeiro as multas de ${pendingPeriods} antes de regularizar esta.`;

    console.log('❌ Regularização sequencial violada:', message);

    return {
      isValid: false,
      message,
      unpaidFines: unpaidPreviousFines
    };
  }

  console.log('✅ Regularização sequencial válida');
  return { isValid: true };
};
