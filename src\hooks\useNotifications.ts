import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getNotificacoes, getNotificacoesUsuario, marcarNotificacaoComoLida, marcarTodasNotificacoesComoLidas, criarNotificacao } from '@/utils/supabase-helpers';
import { supabase } from '@/integrations/supabase/client';

export interface Notification {
  id: string;
  title: string;
  description: string;
  type: 'quota' | 'payment' | 'document' | 'general';
  isRead: boolean;
  timestamp: Date;
  relatedEntityId?: string; // ID da entidade relacionada (quota, pagamento, documento)
  relatedEntityType?: string; // Tipo da entidade relacionada
}

// Helper function to convert database notification to our interface
const convertDbNotificationToInterface = (dbNotification: any): Notification => {
  return {
    id: dbNotification.id,
    title: dbNotification.titulo,
    description: dbNotification.mensagem,
    type: dbNotification.tipo || 'general',
    isRead: dbNotification.lida || false,
    timestamp: new Date(dbNotification.created_at),
    relatedEntityId: undefined, // Not available in current schema
    relatedEntityType: undefined // Not available in current schema
  };
};

export const useNotifications = () => {
  const { user, profile } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch notifications from database
  const fetchNotifications = async () => {
    try {
      setLoading(true);
      setError(null);

      let dbNotifications = [];

      if (profile?.role === 'admin') {
        // Admin sees all notifications
        dbNotifications = await getNotificacoes();
      } else if (profile?.id) {
        // Regular users see only their notifications
        dbNotifications = await getNotificacoesUsuario(profile.id);
      }

      const convertedNotifications = dbNotifications.map(convertDbNotificationToInterface);
      setNotifications(convertedNotifications);
    } catch (err) {
      console.error('Error fetching notifications:', err);
      setError('Erro ao carregar notificações');
    } finally {
      setLoading(false);
    }
  };

  // Load notifications on mount and when user/profile changes
  useEffect(() => {
    if (user && profile) {
      fetchNotifications();
    }
  }, [user, profile]);

  // Set up real-time subscription for notifications
  useEffect(() => {
    if (!user || !profile) return;

    const channel = supabase
      .channel('notifications-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notificacoes',
          filter: profile.role === 'admin' ? undefined : `usuario_id=eq.${profile.id}`
        },
        () => {
          // Refetch notifications when changes occur
          fetchNotifications();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user, profile]);

  // Contar notificações não lidas
  const unreadCount = notifications.filter(n => !n.isRead).length;

  // Marcar uma notificação como lida
  const markAsRead = async (id: string) => {
    try {
      await marcarNotificacaoComoLida(id);
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === id
            ? { ...notification, isRead: true }
            : notification
        )
      );
    } catch (err) {
      console.error('Error marking notification as read:', err);
    }
  };

  // Marcar todas como lidas
  const markAllAsRead = async () => {
    if (!profile?.id) return;

    try {
      await marcarTodasNotificacoesComoLidas(profile.id);
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, isRead: true }))
      );
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
    }
  };

  // Remover notificação por ID da entidade relacionada
  const removeByRelatedEntity = (entityId: string, entityType: string) => {
    setNotifications(prev =>
      prev.filter(notification =>
        !(notification.relatedEntityId === entityId && notification.relatedEntityType === entityType)
      )
    );
  };

  // Adicionar nova notificação (saves to database and updates local state)
  const addNotification = async (notification: Omit<Notification, 'id' | 'timestamp'>) => {
    if (!profile?.id) {
      console.warn('Cannot create notification: user profile not available');
      return;
    }

    try {
      // Save to database
      const dbNotification = await criarNotificacao({
        usuario_id: profile.id,
        titulo: notification.title,
        mensagem: notification.description,
        tipo: notification.type,
        link: undefined // We don't use links in the current schema
      });

      if (dbNotification) {
        // The real-time subscription will automatically update the local state
        console.log('✅ Notification created successfully:', dbNotification);
      }
    } catch (error) {
      console.error('❌ Error creating notification:', error);

      // Fallback: add to local state only if database save fails
      const newNotification: Notification = {
        ...notification,
        id: Date.now().toString(),
        timestamp: new Date()
      };

      setNotifications(prev => [newNotification, ...prev]);
    }
  };

  // Obter notificações recentes (últimas 5 para o dropdown)
  const getRecentNotifications = (limit: number = 5) => {
    return notifications
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  };

  // Obter todas as notificações ordenadas por data
  const getAllNotifications = () => {
    return notifications
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  };

  // Refresh notifications manually
  const refreshNotifications = () => {
    fetchNotifications();
  };

  return {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    removeByRelatedEntity,
    addNotification,
    getRecentNotifications,
    getAllNotifications,
    refreshNotifications
  };
};
