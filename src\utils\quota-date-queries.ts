import { supabase } from '@/integrations/supabase/client';

/**
 * Get all distinct years that have quota or fine data
 * @returns Array of years in descending order (newest first)
 */
export const getAvailableYears = async (): Promise<number[]> => {
  try {
    const { data, error } = await supabase
      .from('quotas')
      .select('ano')
      .order('ano', { ascending: false });

    if (error) {
      console.error('Error fetching available years:', error);
      return [];
    }

    // Get unique years and filter out null/undefined values
    const uniqueYears = [...new Set(data?.map(item => item.ano).filter(Boolean))] as number[];
    
    // Sort in descending order (newest first)
    return uniqueYears.sort((a, b) => b - a);
  } catch (error) {
    console.error('Error in getAvailableYears:', error);
    return [];
  }
};

/**
 * Get all distinct months that have data for a specific year
 * @param year - The year to get months for
 * @returns Array of month numbers (1-12) in ascending order
 */
export const getAvailableMonthsForYear = async (year: number): Promise<number[]> => {
  try {
    const { data, error } = await supabase
      .from('quotas')
      .select('mes')
      .eq('ano', year)
      .order('mes', { ascending: true });

    if (error) {
      console.error('Error fetching available months for year:', year, error);
      return [];
    }

    // Get unique months and filter out null/undefined values
    const uniqueMonths = [...new Set(data?.map(item => item.mes).filter(Boolean))] as number[];
    
    // Sort in ascending order (January to December)
    return uniqueMonths.sort((a, b) => a - b);
  } catch (error) {
    console.error('Error in getAvailableMonthsForYear:', error);
    return [];
  }
};

/**
 * Get all distinct months across all years (for when no year is selected)
 * @returns Array of month numbers (1-12) in ascending order
 */
export const getAllAvailableMonths = async (): Promise<number[]> => {
  try {
    const { data, error } = await supabase
      .from('quotas')
      .select('mes')
      .order('mes', { ascending: true });

    if (error) {
      console.error('Error fetching all available months:', error);
      return [];
    }

    // Get unique months and filter out null/undefined values
    const uniqueMonths = [...new Set(data?.map(item => item.mes).filter(Boolean))] as number[];
    
    // Sort in ascending order (January to December)
    return uniqueMonths.sort((a, b) => a - b);
  } catch (error) {
    console.error('Error in getAllAvailableMonths:', error);
    return [];
  }
};

/**
 * Check if a specific year-month combination has data
 * @param year - The year to check
 * @param month - The month to check
 * @returns Boolean indicating if data exists
 */
export const hasDataForYearMonth = async (year: number, month: number): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('quotas')
      .select('id')
      .eq('ano', year)
      .eq('mes', month)
      .limit(1);

    if (error) {
      console.error('Error checking data for year-month:', year, month, error);
      return false;
    }

    return (data?.length || 0) > 0;
  } catch (error) {
    console.error('Error in hasDataForYearMonth:', error);
    return false;
  }
};

/**
 * Get summary of available data periods
 * @returns Object with earliest and latest year/month combinations
 */
export const getDataPeriodSummary = async (): Promise<{
  earliestYear: number | null;
  latestYear: number | null;
  earliestMonth: number | null;
  latestMonth: number | null;
  totalRecords: number;
}> => {
  try {
    const { data, error } = await supabase
      .from('quotas')
      .select('ano, mes')
      .order('ano', { ascending: true });

    if (error) {
      console.error('Error fetching data period summary:', error);
      return {
        earliestYear: null,
        latestYear: null,
        earliestMonth: null,
        latestMonth: null,
        totalRecords: 0
      };
    }

    if (!data || data.length === 0) {
      return {
        earliestYear: null,
        latestYear: null,
        earliestMonth: null,
        latestMonth: null,
        totalRecords: 0
      };
    }

    const years = data.map(item => item.ano).filter(Boolean);
    const months = data.map(item => item.mes).filter(Boolean);

    return {
      earliestYear: Math.min(...years),
      latestYear: Math.max(...years),
      earliestMonth: Math.min(...months),
      latestMonth: Math.max(...months),
      totalRecords: data.length
    };
  } catch (error) {
    console.error('Error in getDataPeriodSummary:', error);
    return {
      earliestYear: null,
      latestYear: null,
      earliestMonth: null,
      latestMonth: null,
      totalRecords: 0
    };
  }
};
