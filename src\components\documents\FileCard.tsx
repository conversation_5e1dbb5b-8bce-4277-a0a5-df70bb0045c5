
import React from 'react';
import {
  FileIcon,
  FileTextIcon,
  ImageIcon,
  FolderIcon,
  Trash2Icon,
  DownloadIcon,
  ExternalLinkIcon,
  FileText,
  ShareIcon
} from 'lucide-react';
import { 
  Card, 
  CardContent, 
  CardFooter 
} from '@/components/ui/card';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger 
} from '@/components/ui/tooltip';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { DocumentFile } from '@/types';

interface FileCardProps {
  file: DocumentFile;
  onOpen: () => void;
  onDelete: () => void;
  onShare?: () => void;
  getFileUrl: (path: string) => string;
}

const FileCard: React.FC<FileCardProps> = ({ file, onOpen, onDelete, onShare, getFileUrl }) => {

  const handleDownload = async (e: React.MouseEvent) => {
    e.stopPropagation();

    try {
      console.log('Iniciando download do arquivo:', file.name);

      // Obter URL do arquivo
      const fileUrl = getFileUrl(file.fullPath);

      // Fazer fetch do arquivo
      const response = await fetch(fileUrl);
      if (!response.ok) {
        throw new Error('Erro ao baixar arquivo');
      }

      // Converter para blob
      const blob = await response.blob();

      // Criar URL temporária
      const url = window.URL.createObjectURL(blob);

      // Criar elemento de link temporário
      const link = document.createElement('a');
      link.href = url;
      link.download = file.name; // Nome do arquivo para download

      // Adicionar ao DOM, clicar e remover
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Limpar URL temporária
      window.URL.revokeObjectURL(url);

      console.log('Download concluído:', file.name);

    } catch (error) {
      console.error('Erro no download:', error);
      // Fallback: abrir em nova aba se o download falhar
      window.open(getFileUrl(file.fullPath), '_blank');
    }
  };

  const getIcon = () => {
    if (file.isFolder) {
      return <FolderIcon className="w-12 h-12 text-blue-500" />;
    }

    const mimeType = file.metadata.mimetype;

    if (mimeType.includes('pdf')) {
      return <FileTextIcon className="w-12 h-12 text-red-500" />;
    } else if (mimeType.includes('image')) {
      // Para imagens, mostrar preview da imagem real
      const imageUrl = getFileUrl(file.fullPath);
      return (
        <div className="w-12 h-12 rounded-md overflow-hidden border border-gray-200 bg-gray-50">
          <img
            src={imageUrl}
            alt={file.name}
            className="w-full h-full object-cover"
            onError={(e) => {
              // Fallback para ícone se a imagem não carregar
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              const fallback = target.parentElement?.querySelector('.fallback-icon') as HTMLElement;
              if (fallback) {
                fallback.style.display = 'block';
              }
            }}
          />
          <div className="fallback-icon w-full h-full flex items-center justify-center" style={{ display: 'none' }}>
            <ImageIcon className="w-8 h-8 text-green-500" />
          </div>
        </div>
      );
    } else if (mimeType.includes('word') || mimeType.includes('document')) {
      return <FileText className="w-12 h-12 text-blue-600" />;
    } else {
      return <FileIcon className="w-12 h-12 text-gray-500" />;
    }
  };

  const fileSize = () => {
    if (file.isFolder) return '';
    
    const size = file.metadata.size;
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
    return `${(size / (1024 * 1024)).toFixed(1)} MB`;
  };

  const createdAt = new Date(file.created_at);
  const formattedDate = formatDistanceToNow(createdAt, { 
    addSuffix: true,
    locale: ptBR
  });

  return (
    <Card className={cn(
      "w-full overflow-hidden transition-all duration-200 cursor-pointer hover:shadow-md",
      file.isFolder ? "border-blue-200" : ""
    )}>
      <CardContent className="p-4" onClick={onOpen}>
        <div className="flex flex-col items-center justify-center">
          {getIcon()}
          <h3 className="mt-2 text-sm font-medium text-center text-ellipsis overflow-hidden w-full">
            {file.name}
          </h3>
          {!file.isFolder && (
            <p className="text-xs text-gray-500 mt-1">{fileSize()}</p>
          )}
        </div>
      </CardContent>
      <CardFooter className="bg-gray-50 flex items-center justify-between p-2">
        <span className="text-xs text-gray-500">{formattedDate}</span>
        
        <TooltipProvider>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="text-gray-500">•••</span>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Opções</p>
                  </TooltipContent>
                </Tooltip>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={onOpen}>
                {file.isFolder ? (
                  <>
                    <FolderIcon className="mr-2 h-4 w-4" />
                    <span>Abrir</span>
                  </>
                ) : (
                  <>
                    <ExternalLinkIcon className="mr-2 h-4 w-4" />
                    <span>Visualizar</span>
                  </>
                )}
              </DropdownMenuItem>
              
              {!file.isFolder && (
                <DropdownMenuItem onClick={handleDownload}>
                  <DownloadIcon className="mr-2 h-4 w-4" />
                  <span>Baixar</span>
                </DropdownMenuItem>
              )}

              {!file.isFolder && onShare && (
                <DropdownMenuItem onClick={(e) => {
                  e.stopPropagation();
                  onShare();
                }}>
                  <ShareIcon className="mr-2 h-4 w-4" />
                  <span>Disponibilizar para Moradores</span>
                </DropdownMenuItem>
              )}

              <DropdownMenuSeparator />

              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete();
                }}
                className="text-red-600"
              >
                <Trash2Icon className="mr-2 h-4 w-4" />
                <span>Excluir</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </TooltipProvider>
      </CardFooter>
    </Card>
  );
};

export default FileCard;
