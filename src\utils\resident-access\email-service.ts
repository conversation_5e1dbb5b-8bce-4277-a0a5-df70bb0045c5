
import { supabase } from '@/integrations/supabase/client';

/**
 * Envia credenciais por email para o morador
 */
export const sendCredentialsEmail = async (
  credentials: { name: string; email: string; password: string; apartment: string },
  sendToResident: boolean
): Promise<boolean | 'simulated'> => {
  try {
    if (!sendToResident) {
      console.log('📧 [sendCredentialsEmail] No email sending requested');
      return true;
    }

    console.log('📧 [sendCredentialsEmail] Sending credentials email...');

    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (!session?.user) {
      throw new Error('Sessão não encontrada');
    }

    const payload = {
      to: credentials.email,
      name: credentials.name,
      apartment: credentials.apartment,
      email: credentials.email,
      password: credentials.password
    };

    const response = await fetch('https://qrcegsdhbwgjtkebqjok.supabase.co/functions/v1/send-credentials-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFyY2Vnc2RoYndnanRrZWJxam9rIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDI5MTIzNDMsImV4cCI6MjA1ODQ4ODM0M30.r94EU08552kYer4TIGBxlg4h-dkqXt6_tOb1wzSm_lc'
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('❌ [sendCredentialsEmail] Email API error:', errorData);
      throw new Error(errorData.error || errorData.message || 'Erro ao enviar email');
    }

    const result = await response.json();
    console.log('✅ [sendCredentialsEmail] Email sent successfully:', result);

    // Check if email was actually sent or just simulated
    if (result.details?.simulated) {
      console.warn('⚠️ [sendCredentialsEmail] Email was simulated (RESEND_API_KEY not configured)');
      return 'simulated';
    }

    return result.success;
  } catch (error) {
    console.error('❌ [sendCredentialsEmail] Error sending email:', error);
    // Don't throw error - email sending is not critical for user creation
    return false;
  }
};
