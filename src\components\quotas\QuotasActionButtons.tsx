import React from 'react';
import { PlusCircle, Shield } from 'lucide-react';
import { QuotaGenerator } from './QuotaGenerator';

interface QuotasActionButtonsProps {
  onNewQuota: () => void;
  onGenerateQuotas: (quotas: any[]) => void;
  isGenerating: boolean;
}

const QuotasActionButtons: React.FC<QuotasActionButtonsProps> = ({
  onNewQuota,
  onGenerateQuotas,
  isGenerating
}) => {
  return (
    <div className="flex items-center gap-3">
      <QuotaGenerator
        onGenerateQuotas={onGenerateQuotas}
        isGenerating={isGenerating}
      />
      
      <button
        onClick={onNewQuota}
        className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium shadow-sm hover:shadow-md"
      >
        <PlusCircle className="h-4 w-4" />
        <span className="hidden sm:inline">Nova Quota</span>
      </button>
    </div>
  );
};

export default QuotasActionButtons;
