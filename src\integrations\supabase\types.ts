export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      apartment_access: {
        Row: {
          apartment_id: string
          created_at: string
          granted_at: string
          granted_by: string
          id: string
          is_active: boolean
          revoked_at: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          apartment_id: string
          created_at?: string
          granted_at?: string
          granted_by: string
          id?: string
          is_active?: boolean
          revoked_at?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          apartment_id?: string
          created_at?: string
          granted_at?: string
          granted_by?: string
          id?: string
          is_active?: boolean
          revoked_at?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      comunicados: {
        Row: {
          autor: string
          conteudo: string
          created_at: string
          data_publicacao: string
          fixado: boolean | null
          id: string
          prioridade: string
          tipo: string
          titulo: string
          updated_at: string
        }
        Insert: {
          autor: string
          conteudo: string
          created_at?: string
          data_publicacao?: string
          fixado?: boolean | null
          id?: string
          prioridade?: string
          tipo?: string
          titulo: string
          updated_at?: string
        }
        Update: {
          autor?: string
          conteudo?: string
          created_at?: string
          data_publicacao?: string
          fixado?: boolean | null
          id?: string
          prioridade?: string
          tipo?: string
          titulo?: string
          updated_at?: string
        }
        Relationships: []
      }
      comunicados_lidos: {
        Row: {
          comunicado_id: string
          data_leitura: string
          id: string
          usuario_id: string
        }
        Insert: {
          comunicado_id: string
          data_leitura?: string
          id?: string
          usuario_id: string
        }
        Update: {
          comunicado_id?: string
          data_leitura?: string
          id?: string
          usuario_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "comunicados_lidos_comunicado_id_fkey"
            columns: ["comunicado_id"]
            isOneToOne: false
            referencedRelation: "comunicados"
            referencedColumns: ["id"]
          },
        ]
      }
      configuracoes: {
        Row: {
          created_at: string
          descricao: string | null
          id: string
          nome: string
          updated_at: string
          valor: string
        }
        Insert: {
          created_at?: string
          descricao?: string | null
          id?: string
          nome: string
          updated_at?: string
          valor: string
        }
        Update: {
          created_at?: string
          descricao?: string | null
          id?: string
          nome?: string
          updated_at?: string
          valor?: string
        }
        Relationships: []
      }
      document_folders: {
        Row: {
          created_at: string
          created_by: string
          id: string
          name: string
          parent_id: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by: string
          id?: string
          name: string
          parent_id?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string
          id?: string
          name?: string
          parent_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "document_folders_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "document_folders"
            referencedColumns: ["id"]
          },
        ]
      }
      documentos_moradores: {
        Row: {
          apartment_id: string
          categoria: string
          created_at: string
          data_upload: string
          id: string
          nome: string
          tamanho: string
          tipo: string
          uploaded_by: string
          url_arquivo: string
        }
        Insert: {
          apartment_id: string
          categoria: string
          created_at?: string
          data_upload?: string
          id?: string
          nome: string
          tamanho: string
          tipo: string
          uploaded_by: string
          url_arquivo: string
        }
        Update: {
          apartment_id?: string
          categoria?: string
          created_at?: string
          data_upload?: string
          id?: string
          nome?: string
          tamanho?: string
          tipo?: string
          uploaded_by?: string
          url_arquivo?: string
        }
        Relationships: []
      }
      fluxo_caixa: {
        Row: {
          anexo: string | null
          categoria: string
          created_at: string
          data: string
          descricao: string
          id: string
          responsavel_id: string | null
          tipo: string
          updated_at: string
          valor: number
        }
        Insert: {
          anexo?: string | null
          categoria: string
          created_at?: string
          data: string
          descricao: string
          id?: string
          responsavel_id?: string | null
          tipo: string
          updated_at?: string
          valor: number
        }
        Update: {
          anexo?: string | null
          categoria?: string
          created_at?: string
          data?: string
          descricao?: string
          id?: string
          responsavel_id?: string | null
          tipo?: string
          updated_at?: string
          valor?: number
        }
        Relationships: []
      }
      membros_comissao: {
        Row: {
          biografia: string | null
          cargo: string
          created_at: string
          data_fim: string | null
          data_inicio: string
          email: string | null
          foto: string | null
          id: string
          nome: string
          status: string | null
          telefone: string | null
          updated_at: string
        }
        Insert: {
          biografia?: string | null
          cargo: string
          created_at?: string
          data_fim?: string | null
          data_inicio: string
          email?: string | null
          foto?: string | null
          id?: string
          nome: string
          status?: string | null
          telefone?: string | null
          updated_at?: string
        }
        Update: {
          biografia?: string | null
          cargo?: string
          created_at?: string
          data_fim?: string | null
          data_inicio?: string
          email?: string | null
          foto?: string | null
          id?: string
          nome?: string
          status?: string | null
          telefone?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      moradores: {
        Row: {
          apartamento: string
          created_at: string
          email: string
          id: string
          isento_quotas: boolean | null
          motivo_isencao: string | null
          nome: string
          status: string | null
          telefone: string | null
          updated_at: string
          user_id: string | null
        }
        Insert: {
          apartamento: string
          created_at?: string
          email: string
          id?: string
          isento_quotas?: boolean | null
          motivo_isencao?: string | null
          nome: string
          status?: string | null
          telefone?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          apartamento?: string
          created_at?: string
          email?: string
          id?: string
          isento_quotas?: boolean | null
          motivo_isencao?: string | null
          nome?: string
          status?: string | null
          telefone?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Relationships: []
      }
      notificacoes: {
        Row: {
          created_at: string
          id: string
          lida: boolean | null
          link: string | null
          mensagem: string
          tipo: string
          titulo: string
          usuario_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          lida?: boolean | null
          link?: string | null
          mensagem: string
          tipo: string
          titulo: string
          usuario_id: string
        }
        Update: {
          created_at?: string
          id?: string
          lida?: boolean | null
          link?: string | null
          mensagem?: string
          tipo?: string
          titulo?: string
          usuario_id?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          apartment_id: string | null
          banned: boolean | null
          created_at: string
          email: string | null
          first_login: boolean | null
          id: string
          last_sign_in: string | null
          name: string | null
          photo: string | null
          role: string | null
          updated_at: string
        }
        Insert: {
          apartment_id?: string | null
          banned?: boolean | null
          created_at?: string
          email?: string | null
          first_login?: boolean | null
          id: string
          last_sign_in?: string | null
          name?: string | null
          photo?: string | null
          role?: string | null
          updated_at?: string
        }
        Update: {
          apartment_id?: string | null
          banned?: boolean | null
          created_at?: string
          email?: string | null
          first_login?: boolean | null
          id?: string
          last_sign_in?: string | null
          name?: string | null
          photo?: string | null
          role?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      quotas: {
        Row: {
          ano: number
          comprovativo: string | null
          created_at: string
          data_pagamento: string | null
          data_vencimento: string
          fluxo_caixa_id: string | null
          id: string
          isento: boolean | null
          mes: number
          morador_id: string
          multa: number | null
          numero_multas: number | null
          quotas_atraso: number | null
          situacao: string | null
          status: string | null
          updated_at: string
          valor: number
        }
        Insert: {
          ano: number
          comprovativo?: string | null
          created_at?: string
          data_pagamento?: string | null
          data_vencimento: string
          fluxo_caixa_id?: string | null
          id?: string
          isento?: boolean | null
          mes: number
          morador_id: string
          multa?: number | null
          numero_multas?: number | null
          quotas_atraso?: number | null
          situacao?: string | null
          status?: string | null
          updated_at?: string
          valor: number
        }
        Update: {
          ano?: number
          comprovativo?: string | null
          created_at?: string
          data_pagamento?: string | null
          data_vencimento?: string
          fluxo_caixa_id?: string | null
          id?: string
          isento?: boolean | null
          mes?: number
          morador_id?: string
          multa?: number | null
          numero_multas?: number | null
          quotas_atraso?: number | null
          situacao?: string | null
          status?: string | null
          updated_at?: string
          valor?: number
        }
        Relationships: [
          {
            foreignKeyName: "quotas_fluxo_caixa_id_fkey"
            columns: ["fluxo_caixa_id"]
            isOneToOne: false
            referencedRelation: "fluxo_caixa"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quotas_morador_id_fkey"
            columns: ["morador_id"]
            isOneToOne: false
            referencedRelation: "moradores"
            referencedColumns: ["id"]
          },
        ]
      }
      relatorios: {
        Row: {
          created_at: string
          gerado_por: string
          id: string
          parametros: Json | null
          resultado: Json | null
          tipo: string
          titulo: string
        }
        Insert: {
          created_at?: string
          gerado_por: string
          id?: string
          parametros?: Json | null
          resultado?: Json | null
          tipo: string
          titulo: string
        }
        Update: {
          created_at?: string
          gerado_por?: string
          id?: string
          parametros?: Json | null
          resultado?: Json | null
          tipo?: string
          titulo?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      calcular_saldo: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      fix_morador_user_id_consistency: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      get_all_users: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          name: string
          email: string
          role: string
          banned: boolean
          last_sign_in: string
          created_at: string
          updated_at: string
        }[]
      }
      get_residents_with_access_status: {
        Args: Record<PropertyKey, never>
        Returns: {
          morador_id: string
          morador_nome: string
          morador_email: string
          apartamento: string
          has_access: boolean
          user_id: string
          access_granted_at: string
          first_login: boolean
        }[]
      }
      get_user_details_by_email: {
        Args: { p_email: string }
        Returns: {
          user_id: string
          name: string
          email: string
          role: string
          apartment_id: string
          has_active_access: boolean
          created_at: string
        }[]
      }
      is_current_user_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      remove_resident_completely: {
        Args: { p_user_id: string; p_apartment_id: string }
        Returns: Json
      }
      revoke_apartment_access: {
        Args: { p_user_id: string; p_apartment_id: string }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
