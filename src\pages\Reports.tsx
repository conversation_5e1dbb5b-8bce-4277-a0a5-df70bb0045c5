
import React, { useState } from 'react';
import Sidebar from '@/components/layout/Sidebar';
import Header from '@/components/layout/Header';
import DashboardCard from '@/components/dashboard/DashboardCard';
import Chart from '@/components/dashboard/Chart';
import { ReportFiltersModal } from '@/components/reports/ReportFiltersModal';
import { useSidebar } from '@/contexts/SidebarContext';
import { useReports } from '@/hooks/useReports';
import { useDashboardData } from '@/hooks/useDashboardData';
import { cn } from '@/lib/utils';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import {
  FileBarChart2,
  Download,
  CreditCard,
  Users,
  Building2,
  Wallet,
  History,
  AlertTriangle,
  Shield
} from 'lucide-react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ReportType } from '@/types/reports';



const Reports = () => {
  const { collapsed, isMobile } = useSidebar();
  const [activeTab, setActiveTab] = useState('quotas');

  // Hook personalizado para gerenciar relatórios
  const {
    isGenerating,
    showFiltersModal,
    currentReportType,
    openFiltersModal,
    closeFiltersModal,
    generateReport,
    getReportHistory,
    formatReportTitle
  } = useReports();

  // Hook para dados do dashboard (para gráficos e estatísticas)
  const { chartData, stats, quotasSummary, financialSummary, isLoading } = useDashboardData();

  // Query para buscar dados reais da comissão
  const { data: membrosComissao = [], isLoading: isLoadingComissao } = useQuery({
    queryKey: ['membros_comissao'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('membros_comissao')
        .select('*')
        .order('nome', { ascending: true });

      if (error) throw error;
      return data || [];
    },
    staleTime: 5 * 60 * 1000, // Cache por 5 minutos
  });

  // Histórico de relatórios recentes
  const recentReports = getReportHistory();

  /**
   * Formata valores monetários no padrão angolano
   */
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('pt-AO', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value) + ' Kz';
  };

  /**
   * Calcula percentual de forma segura limitando entre 0 e 100%
   */
  const calculatePercentage = (part: number, total: number): number => {
    if (total <= 0) return 0;
    const percentage = (part / total) * 100;
    // Limitar percentagem entre 0 e 100%
    return Math.max(0, Math.min(100, percentage));
  };

  /**
   * Manipula a abertura do modal de filtros
   */
  const handleGenerateReport = (reportType: ReportType) => {
    openFiltersModal(reportType);
  };

  /**
   * Manipula a geração do relatório após configurar filtros
   */
  const handleConfirmGeneration = async (filters: any) => {
    if (currentReportType) {
      await generateReport(currentReportType, filters);
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />

      <div className={cn(
        "flex-1 flex flex-col transition-all duration-300 overflow-hidden",
        isMobile ? "ml-0" : (collapsed ? "ml-12" : "ml-64")
      )}>
        <div className="flex-1 overflow-y-auto pb-10">
          <div className="page-container">
            <Header
              title="Relatórios"
              subtitle="Visualize e baixe relatórios do condomínio"
            />

            {/* Seção de controles */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mt-6">
              <div className="flex items-center gap-2">
                <h2 className="text-lg font-semibold text-gray-800">Relatórios Disponíveis</h2>
                {recentReports.length > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {recentReports.length} recente{recentReports.length !== 1 ? 's' : ''}
                  </Badge>
                )}
              </div>


            </div>

            <div className="mt-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 animate-enter">
               <DashboardCard
                title="Saldo"
                value={
                  isGenerating && currentReportType === 'financial'
                    ? 'Gerando...'
                    : isLoading
                      ? 'Carregando...'
                      : formatCurrency(financialSummary?.saldoAtual || 0)
                }
                icon={Wallet}
                description={
                  isLoading
                    ? 'Carregando dados...'
                    : `${formatCurrency(financialSummary?.entradaMes || 0)} entrada mensal`
                }
                trend={
                  !isLoading && financialSummary ? {
                    value: Math.round(Math.abs(financialSummary.tendenciaEntrada || 0)),
                    isPositive: (financialSummary.tendenciaEntrada || 0) >= 0
                  } : undefined
                }
                iconBgColor="bg-purple-100"
                iconColor="text-purple-500"
                isLoading={isLoading}
              />
              
              <DashboardCard
                title="Quotas"
                value={
                  isGenerating && currentReportType === 'quotas'
                    ? 'Gerando...'
                    : isLoading
                      ? 'Carregando...'
                      : `${quotasSummary?.quotasPendentes || 0} Pendentes`
                }
                icon={CreditCard}
                description={
                  isLoading
                    ? 'Carregando dados...'
                    : `${formatCurrency(quotasSummary?.valorPendente || 0)} em atraso`
                }
                trend={
                  !isLoading && quotasSummary ? {
                    value: calculatePercentage(quotasSummary.quotasPagas, quotasSummary.totalQuotas),
                    isPositive: true
                  } : undefined
                }
                iconBgColor="bg-primary-100"
                iconColor="text-primary-500"
                isLoading={isLoading}
              />

              <DashboardCard
                title="Multas"
                value={
                  isGenerating && currentReportType === 'residents'
                    ? 'Gerando...'
                    : isLoading
                      ? 'Carregando...'
                      : `${stats?.moradoresComMultas || 0} Moradores`
                }
                icon={Users}
                description={
                  isLoading
                    ? 'Carregando dados...'
                    : `Total com multas aplicadas`
                }
                trend={
                  !isLoading && stats ? {
                    value: stats.tendenciaMultas || 0,
                    isPositive: (stats.tendenciaMultas || 0) <= 0
                  } : undefined
                }
                iconBgColor="bg-orange-100"
                iconColor="text-orange-500"
                isLoading={isLoading}
              />

             

              <DashboardCard
                title="Comissão"
                value={
                  isGenerating && currentReportType === 'committee'
                    ? 'Gerando...'
                    : isLoadingComissao
                      ? 'Carregando...'
                      : `${membrosComissao.length} Membros`
                }
                icon={Building2}
                description={
                  isLoadingComissao
                    ? 'Carregando dados...'
                    : membrosComissao.length === 0
                      ? 'Nenhum membro cadastrado'
                      : `${membrosComissao.filter(m => m.status === 'Ativo' || !m.status).length} ativos • ${membrosComissao.length} total`
                }
                iconBgColor="bg-orange-100"
                iconColor="text-orange-500"
                isLoading={isLoadingComissao}
              />
            </div>

            {/* Seção de Abas com Relatórios */}
            <div className="mt-8">
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                <div className="border-b border-gray-200">
                  <ul className="flex overflow-x-auto">
                    <li className="flex-shrink-0">
                      <button
                        className={`flex items-center gap-2 px-4 py-3 text-sm font-medium border-b-2 ${
                          activeTab === 'quotas'
                            ? 'border-primary text-primary'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                        onClick={() => setActiveTab('quotas')}
                      >
                        <CreditCard className="h-4 w-4" />
                        Relatório de Quotas
                      </button>
                    </li>
                    <li className="flex-shrink-0">
                      <button
                        className={`flex items-center gap-2 px-4 py-3 text-sm font-medium border-b-2 ${
                          activeTab === 'residents'
                            ? 'border-primary text-primary'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                        onClick={() => setActiveTab('residents')}
                      >
                        <Users className="h-4 w-4" />
                        Relatório de Moradores
                      </button>
                    </li>
                    <li className="flex-shrink-0">
                      <button
                        className={`flex items-center gap-2 px-4 py-3 text-sm font-medium border-b-2 ${
                          activeTab === 'fines'
                            ? 'border-primary text-primary'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                        onClick={() => setActiveTab('fines')}
                      >
                        <AlertTriangle className="h-4 w-4" />
                        Relatório de Multas
                      </button>
                    </li>
                    <li className="flex-shrink-0">
                      <button
                        className={`flex items-center gap-2 px-4 py-3 text-sm font-medium border-b-2 ${
                          activeTab === 'financial'
                            ? 'border-primary text-primary'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                        onClick={() => setActiveTab('financial')}
                      >
                        <Wallet className="h-4 w-4" />
                        Relatório Financeiro
                      </button>
                    </li>
                    <li className="flex-shrink-0">
                      <button
                        className={`flex items-center gap-2 px-4 py-3 text-sm font-medium border-b-2 ${
                          activeTab === 'committee'
                            ? 'border-primary text-primary'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                        onClick={() => setActiveTab('committee')}
                      >
                        <Building2 className="h-4 w-4" />
                        Relatório da Comissão
                      </button>
                    </li>
                  </ul>
                </div>

                <div className="p-6">
                  {activeTab === 'quotas' && (
                    <div className="animate-fade-in">
                      <div className="mb-6">
                        <h3 className="text-lg font-semibold text-gray-800 mb-2">Relatório de Quotas</h3>
                        <p className="text-gray-600">Visualize o histórico de pagamentos de quotas e gere relatórios detalhados.</p>
                      </div>

                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <Chart
                          type="bar"
                          data={chartData?.monthlyQuotas || []}
                          title="Quotas Mensais"
                          subtitle="Comparativo de quotas pagas e pendentes"
                          dataKeys={['pagas', 'pendentes']}
                          height={300}
                          colors={['#019cdf', '#FDA349']}
                          isLoading={isLoading}
                        />

                        <div className="space-y-4">
                          <div className="p-4 bg-gray-50 rounded-lg">
                            <h4 className="font-medium text-gray-800 mb-3">Estatísticas de Quotas</h4>
                            <div className="space-y-3">
                              <div className="flex justify-between">
                                <span className="text-gray-600">Total de Quotas (Mês Atual):</span>
                                <span className="font-medium">{quotasSummary?.totalQuotas || 0}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Quotas Pagas:</span>
                                <span className="font-medium text-green-600">
                                  {quotasSummary?.quotasPagas || 0} ({quotasSummary?.percentualPago.toFixed(0) || 0}%)
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Quotas Pendentes:</span>
                                <span className="font-medium text-red-600">
                                  {quotasSummary?.quotasPendentes || 0} ({(100 - (quotasSummary?.percentualPago || 0)).toFixed(0)}%)
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Valor Total Arrecadado:</span>
                                <span className="font-medium">
                                  {quotasSummary?.valorPago.toLocaleString() || 0} Kz
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Valor Total Pendente:</span>
                                <span className="font-medium">
                                  {quotasSummary?.valorPendente.toLocaleString() || 0} Kz
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className="p-4 bg-primary-50 rounded-lg border border-primary-100">
                            <h4 className="font-medium text-primary-800 mb-3">Opções de Relatório</h4>
                            <div className="space-y-3">
                              <div className="flex justify-between items-center">
                                <span className="text-gray-700">Relatório de Quotas (Completo)</span>
                                <Button
                                  size="sm"
                                  onClick={() => handleGenerateReport('quotas')}
                                  disabled={isGenerating && currentReportType === 'quotas'}
                                  className="text-xs"
                                >
                                  {isGenerating && currentReportType === 'quotas' ? 'Gerando...' : (
                                    <>
                                      <Download className="h-3 w-3 mr-1" />
                                      Gerar PDF
                                    </>
                                  )}
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeTab === 'residents' && (
                    <div className="animate-fade-in">
                      <div className="mb-6">
                        <h3 className="text-lg font-semibold text-gray-800 mb-2">Relatório de Moradores</h3>
                        <p className="text-gray-600">Visualize informações sobre os moradores e gere relatórios detalhados.</p>
                      </div>

                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <h4 className="font-medium text-gray-800 mb-3">Estatísticas de Moradores</h4>
                          <div className="space-y-3">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Total de Moradores:</span>
                              <span className="font-medium">{stats?.totalMoradores || 0}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Moradores Ativos:</span>
                              <span className="font-medium text-green-600">{stats?.moradoresAtivos || 0}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Moradores com Quotas em Dia:</span>
                              <span className="font-medium text-green-600">
                                {quotasSummary?.quotasPagas || 0} ({quotasSummary ? ((quotasSummary.quotasPagas / (quotasSummary.totalQuotas || 1)) * 100).toFixed(0) : 0}%)
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Moradores com Quotas em Atraso:</span>
                              <span className="font-medium text-red-600">
                                {stats?.quotasEmAtraso || 0} ({stats?.percentualAtraso.toFixed(0) || 0}%)
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="p-4 bg-primary-50 rounded-lg border border-primary-100">
                          <h4 className="font-medium text-primary-800 mb-3">Opções de Relatório</h4>
                          <div className="space-y-3">
                            <div className="flex justify-between items-center">
                              <span className="text-gray-700">Lista Completa de Moradores</span>
                              <Button
                                size="sm"
                                onClick={() => handleGenerateReport('residents')}
                                disabled={isGenerating && currentReportType === 'residents'}
                                className="text-xs"
                              >
                                {isGenerating && currentReportType === 'residents' ? 'Gerando...' : (
                                  <>
                                    <Download className="h-3 w-3 mr-1" />
                                    Gerar PDF
                                  </>
                                )}
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeTab === 'fines' && (
                    <div className="animate-fade-in">
                      <div className="mb-6">
                        <h3 className="text-lg font-semibold text-gray-800 mb-2">Relatório de Multas</h3>
                        <p className="text-gray-600">Visualize informações sobre multas aplicadas e gere relatórios detalhados.</p>
                      </div>

                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <h4 className="font-medium text-gray-800 mb-3">Estatísticas de Multas</h4>
                          <div className="space-y-3">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Moradores com Multas:</span>
                              <span className="font-medium text-orange-600">{stats?.moradoresComMultas || 0}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Total de Moradores:</span>
                              <span className="font-medium">{stats?.totalMoradores || 0}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Percentual com Multas:</span>
                              <span className="font-medium text-orange-600">
                                {stats?.totalMoradores ? ((stats.moradoresComMultas / stats.totalMoradores) * 100).toFixed(1) : 0}%
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Tendência:</span>
                              <span className={`font-medium ${(stats?.tendenciaMultas || 0) <= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {(stats?.tendenciaMultas || 0) > 0 ? '+' : ''}{stats?.tendenciaMultas?.toFixed(1) || 0}%
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="p-4 bg-orange-50 rounded-lg border border-orange-100">
                          <h4 className="font-medium text-orange-800 mb-3">Opções de Relatório</h4>
                          <div className="space-y-3">
                            <div className="flex justify-between items-center">
                              <span className="text-gray-700">Relatório de Multas (Completo)</span>
                              <Button
                                size="sm"
                                onClick={() => handleGenerateReport('fines')}
                                disabled={isGenerating && currentReportType === 'fines'}
                                className="text-xs bg-orange-600 hover:bg-orange-700"
                              >
                                {isGenerating && currentReportType === 'fines' ? 'Gerando...' : (
                                  <>
                                    <Download className="h-3 w-3 mr-1" />
                                    Gerar PDF
                                  </>
                                )}
                              </Button>
                            </div>
                            <p className="text-xs text-gray-600">
                              Inclui detalhes de todas as multas aplicadas, valores e status de pagamento.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeTab === 'financial' && (
                    <div className="animate-fade-in">
                      <div className="mb-6">
                        <h3 className="text-lg font-semibold text-gray-800 mb-2">Relatório Financeiro</h3>
                        <p className="text-gray-600">Visualize o histórico financeiro e gere relatórios detalhados.</p>
                      </div>

                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <Chart
                          type="area"
                          data={chartData?.cashFlow || []}
                          title="Fluxo de Caixa"
                          subtitle="Entradas e saídas mensais"
                          dataKeys={['entrada', 'saida']}
                          height={300}
                          colors={['#45B69C', '#F87171']}
                          isLoading={isLoading}
                        />

                        <div className="space-y-4">
                          <div className="p-4 bg-gray-50 rounded-lg">
                            <h4 className="font-medium text-gray-800 mb-3">Estatísticas Financeiras</h4>
                            <div className="space-y-3">
                              <div className="flex justify-between">
                                <span className="text-gray-600">Saldo Atual:</span>
                                <span className="font-medium">
                                  {financialSummary?.saldoAtual.toLocaleString() || 0} Kz
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Total de Entradas:</span>
                                <span className="font-medium text-green-600">
                                  {financialSummary?.totalEntradas.toLocaleString() || 0} Kz
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Total de Saídas:</span>
                                <span className="font-medium text-red-600">
                                  {financialSummary?.totalSaidas.toLocaleString() || 0} Kz
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Entrada do Mês:</span>
                                <span className="font-medium text-green-600">
                                  {financialSummary?.entradaMes.toLocaleString() || 0} Kz
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Saída do Mês:</span>
                                <span className="font-medium text-red-600">
                                  {financialSummary?.saidaMes.toLocaleString() || 0} Kz
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className="p-4 bg-primary-50 rounded-lg border border-primary-100">
                            <h4 className="font-medium text-primary-800 mb-3">Opções de Relatório</h4>
                            <div className="space-y-3">
                              <div className="flex justify-between items-center">
                                <span className="text-gray-700">Relatório Financeiro (Completo)</span>
                                <Button
                                  size="sm"
                                  onClick={() => handleGenerateReport('financial')}
                                  disabled={isGenerating && currentReportType === 'financial'}
                                  className="text-xs"
                                >
                                  {isGenerating && currentReportType === 'financial' ? 'Gerando...' : (
                                    <>
                                      <Download className="h-3 w-3 mr-1" />
                                      Gerar PDF
                                    </>
                                  )}
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeTab === 'committee' && (
                    <div className="animate-fade-in">
                      <div className="mb-6">
                        <h3 className="text-lg font-semibold text-gray-800 mb-2">Relatório da Comissão</h3>
                        <p className="text-gray-600">Visualize informações sobre os membros da comissão e gere relatórios detalhados.</p>
                      </div>

                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <h4 className="font-medium text-gray-800 mb-3">Estatísticas da Comissão</h4>
                          {isLoadingComissao ? (
                            <div className="space-y-3">
                              <div className="animate-pulse">
                                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                                <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                              </div>
                            </div>
                          ) : (
                            <div className="space-y-3">
                              <div className="flex justify-between">
                                <span className="text-gray-600">Total de Membros:</span>
                                <span className="font-medium">{membrosComissao.length}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Membros Ativos:</span>
                                <span className="font-medium text-green-600">
                                  {membrosComissao.filter(m => m.status === 'Ativo' || !m.status).length}
                                </span>
                              </div>
                              {membrosComissao.length === 0 ? (
                                <div className="text-center py-4">
                                  <p className="text-gray-500 text-sm">Nenhum membro cadastrado</p>
                                  <p className="text-gray-400 text-xs mt-1">
                                    Adicione membros na página "Membros da Comissão"
                                  </p>
                                </div>
                              ) : (
                                <>
                                  {/* Mostrar cargos dos membros cadastrados */}
                                  {membrosComissao.slice(0, 4).map((membro) => (
                                    <div key={membro.id} className="flex justify-between">
                                      <span className="text-gray-600">{membro.cargo}:</span>
                                      <span className="font-medium">{membro.nome}</span>
                                    </div>
                                  ))}
                                  {membrosComissao.length > 4 && (
                                    <div className="flex justify-between">
                                      <span className="text-gray-600">Outros:</span>
                                      <span className="font-medium text-primary">
                                        +{membrosComissao.length - 4} membros
                                      </span>
                                    </div>
                                  )}
                                </>
                              )}
                            </div>
                          )}
                        </div>

                        <div className="p-4 bg-primary-50 rounded-lg border border-primary-100">
                          <h4 className="font-medium text-primary-800 mb-3">Opções de Relatório</h4>
                          <div className="space-y-3">
                            <div className="flex justify-between items-center">
                              <span className="text-gray-700">Lista Completa da Comissão</span>
                              <Button
                                size="sm"
                                onClick={() => handleGenerateReport('committee')}
                                disabled={isGenerating && currentReportType === 'committee'}
                                className="text-xs"
                              >
                                {isGenerating && currentReportType === 'committee' ? 'Gerando...' : (
                                  <>
                                    <Download className="h-3 w-3 mr-1" />
                                    Gerar PDF
                                  </>
                                )}
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Seção de Histórico de Relatórios */}
            {recentReports.length > 0 && (
              <div className="mt-8">
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                  <div className="p-6 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <History className="h-5 w-5 text-primary" />
                        <h3 className="text-lg font-semibold text-gray-800">Relatórios Recentes</h3>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {recentReports.length} relatório{recentReports.length !== 1 ? 's' : ''}
                      </Badge>
                    </div>
                  </div>

                  <div className="p-6">
                    <div className="space-y-3">
                      {recentReports.slice(0, 5).map((report) => (
                        <div
                          key={report.id}
                          className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                        >
                          <div className="flex items-center gap-3">
                            <div className="p-2 bg-primary-100 rounded-lg">
                              <FileBarChart2 className="h-4 w-4 text-primary" />
                            </div>
                            <div>
                              <p className="font-medium text-gray-800">{report.title}</p>
                              <p className="text-sm text-gray-500">
                                Gerado em {new Date(report.generatedAt).toLocaleDateString('pt-PT')}
                              </p>
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <Badge
                              variant={report.status === 'completed' ? 'default' : 'destructive'}
                              className="text-xs"
                            >
                              {report.status === 'completed' ? 'Concluído' : 'Erro'}
                            </Badge>
                            {report.status === 'completed' && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  // TODO: Implementar download do relatório
                                  toast.info('Funcionalidade de download será implementada');
                                }}
                              >
                                <Download className="h-3 w-3 mr-1" />
                                Baixar
                              </Button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Modal de Filtros */}
            <ReportFiltersModal
              isOpen={showFiltersModal}
              onClose={closeFiltersModal}
              onGenerate={handleConfirmGeneration}
              reportType={currentReportType || 'quotas'}
              isGenerating={isGenerating}
            />


          </div>
        </div>
      </div>
    </div>
  );
};

export default Reports;
