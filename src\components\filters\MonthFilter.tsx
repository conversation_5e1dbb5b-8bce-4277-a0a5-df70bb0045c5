import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown, Calendar, X } from 'lucide-react';
import { useAvailableMonths } from '@/hooks/useQuotaDateFilters';

interface MonthFilterProps {
  selectedMonth: number | null;
  onMonthChange: (month: number | null) => void;
  selectedYear?: number | null; // Add selectedYear to filter months by year
  className?: string;
}

const MONTHS = [
  { value: 1, label: 'Janeiro' },
  { value: 2, label: 'Fevereiro' },
  { value: 3, label: 'Março' },
  { value: 4, label: 'Abril' },
  { value: 5, label: '<PERSON><PERSON>' },
  { value: 6, label: 'Junho' },
  { value: 7, label: 'Julho' },
  { value: 8, label: 'Agosto' },
  { value: 9, label: 'Setembro' },
  { value: 10, label: 'Outubro' },
  { value: 11, label: 'Novembro' },
  { value: 12, label: 'Dezemb<PERSON>' }
];

const MonthFilter: React.FC<MonthFilterProps> = ({
  selectedMonth,
  onMonthChange,
  selectedYear = null,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Get available months based on selected year
  const { data: availableMonths = [], isLoading: isLoadingMonths } = useAvailableMonths(selectedYear);

  // Clear selected month if it's not available for the selected year
  useEffect(() => {
    if (selectedMonth && availableMonths.length > 0 && !availableMonths.includes(selectedMonth)) {
      onMonthChange(null);
    }
  }, [selectedMonth, availableMonths, onMonthChange]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleMonthSelect = (month: number) => {
    onMonthChange(month);
    setIsOpen(false);
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onMonthChange(null);
  };

  // Filter MONTHS to only show months that have data
  const availableMonthsData = MONTHS.filter(month =>
    availableMonths.includes(month.value)
  );

  const selectedMonthLabel = selectedMonth
    ? MONTHS.find(m => m.value === selectedMonth)?.label
    : null;

  return (
    <div className={cn("relative", className)} ref={dropdownRef}>
      <div
        className={cn(
          "flex items-center justify-between px-3 py-2 border rounded-lg cursor-pointer transition-all duration-200 min-w-[120px]",
          "hover:border-gray-400 focus-within:border-primary-500 focus-within:ring-2 focus-within:ring-primary-200",
          selectedMonth ? "border-primary-300 bg-primary-50" : "border-gray-300 bg-white"
        )}
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex items-center gap-2 flex-1">
          <Calendar size={16} className="text-gray-400 flex-shrink-0" />
          <span className={cn(
            "text-sm truncate",
            selectedMonth ? "text-primary-700 font-medium" : "text-gray-500"
          )}>
            {isLoadingMonths ? "Carregando..." : (selectedMonthLabel || "Meses")}
          </span>
        </div>
        
        <div className="flex items-center gap-1">
          {selectedMonth && (
            <button
              onClick={handleClear}
              className="p-1 hover:bg-gray-100 rounded-full transition-colors"
              title="Limpar filtro"
            >
              <X size={14} className="text-gray-400" />
            </button>
          )}
          <ChevronDown 
            size={16} 
            className={cn(
              "text-gray-400 transition-transform",
              isOpen && "transform rotate-180"
            )} 
          />
        </div>
      </div>

      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto">
          <div
            className="px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm border-b border-gray-100"
            onClick={() => onMonthChange(null)}
          >
            <span className="text-gray-700">Meses</span>
          </div>
          {isLoadingMonths ? (
            <div className="px-3 py-2 text-sm text-gray-500">
              Carregando meses disponíveis...
            </div>
          ) : availableMonthsData.length === 0 ? (
            <div className="px-3 py-2 text-sm text-gray-500">
              {selectedYear ? `Nenhum mês com dados para ${selectedYear}` : "Nenhum mês com dados encontrado"}
            </div>
          ) : (
            availableMonthsData.map((month) => (
              <div
                key={month.value}
                className={cn(
                  "px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm transition-colors",
                  selectedMonth === month.value && "bg-primary-50 text-primary-700 font-medium"
                )}
                onClick={() => handleMonthSelect(month.value)}
              >
                {month.label}
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
};

export default MonthFilter;
