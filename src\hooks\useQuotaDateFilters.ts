import { useQuery } from '@tanstack/react-query';
import { 
  getAvailableYears, 
  getAvailableMonthsForYear, 
  getAllAvailableMonths,
  getDataPeriodSummary 
} from '@/utils/quota-date-queries';

/**
 * Hook to get available years with caching
 */
export const useAvailableYears = () => {
  return useQuery({
    queryKey: ['quota-available-years'],
    queryFn: getAvailableYears,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2, // Retry failed requests
    onError: (error) => {
      console.error('Error fetching available years:', error);
    },
  });
};

/**
 * Hook to get available months for a specific year
 * @param year - The year to get months for (null means get all months)
 */
export const useAvailableMonths = (year: number | null) => {
  return useQuery({
    queryKey: ['quota-available-months', year],
    queryFn: () => {
      if (year === null) {
        return getAllAvailableMonths();
      }
      return getAvailableMonthsForYear(year);
    },
    enabled: true, // Always enabled, will fetch all months if year is null
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    retry: 2, // Retry failed requests
    onError: (error) => {
      console.error('Error fetching available months for year:', year, error);
    },
  });
};

/**
 * Hook to get data period summary
 */
export const useDataPeriodSummary = () => {
  return useQuery({
    queryKey: ['quota-data-period-summary'],
    queryFn: getDataPeriodSummary,
    staleTime: 10 * 60 * 1000, // 10 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false,
  });
};

/**
 * Helper hook that provides both years and months with proper dependencies
 * @param selectedYear - Currently selected year
 */
export const useQuotaDateFilters = (selectedYear: number | null) => {
  const yearsQuery = useAvailableYears();
  const monthsQuery = useAvailableMonths(selectedYear);
  
  return {
    // Years data
    availableYears: yearsQuery.data || [],
    isLoadingYears: yearsQuery.isLoading,
    yearsError: yearsQuery.error,
    
    // Months data
    availableMonths: monthsQuery.data || [],
    isLoadingMonths: monthsQuery.isLoading,
    monthsError: monthsQuery.error,
    
    // Combined loading state
    isLoading: yearsQuery.isLoading || monthsQuery.isLoading,
    
    // Refetch functions
    refetchYears: yearsQuery.refetch,
    refetchMonths: monthsQuery.refetch,
  };
};
