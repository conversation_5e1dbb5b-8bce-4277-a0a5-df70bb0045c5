import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Calendar, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAvailableYears } from '@/hooks/useQuotaDateFilters';

interface YearFilterProps {
  selectedYear: number | null;
  onYearChange: (year: number | null) => void;
  className?: string;
}

const YearFilter: React.FC<YearFilterProps> = ({
  selectedYear,
  onYearChange,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Get available years from database
  const { data: years = [], isLoading: isLoadingYears } = useAvailableYears();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleYearSelect = (year: number) => {
    onYearChange(year);
    setIsOpen(false);
  };

  const handleClearYear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onYearChange(null);
  };

  return (
    <div className={cn("relative", className)} ref={dropdownRef}>
      <button
        type="button"
        className={cn(
          "flex items-center justify-between w-full sm:w-32 px-3 py-2 text-sm",
          "border border-gray-300 rounded-md bg-white hover:bg-gray-50",
          "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
          "transition-colors duration-200",
          isOpen && "ring-2 ring-blue-500 border-blue-500"
        )}
        onClick={() => setIsOpen(!isOpen)}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
      >
        <div className="flex items-center gap-2 min-w-0">
          <Calendar className="h-4 w-4 text-gray-400 flex-shrink-0" />
          <span className={cn(
            "truncate",
            selectedYear ? "text-gray-900" : "text-gray-500"
          )}>
            {isLoadingYears ? "Carregando..." : (selectedYear || "Anos")}
          </span>
        </div>
        <div className="flex items-center gap-1 flex-shrink-0">
          {selectedYear && (
            <button
              type="button"
              onClick={handleClearYear}
              className="p-0.5 hover:bg-gray-200 rounded-sm transition-colors"
              aria-label="Limpar filtro de ano"
            >
              <X className="h-3 w-3 text-gray-400" />
            </button>
          )}
          <ChevronDown className={cn(
            "h-4 w-4 text-gray-400 transition-transform duration-200",
            isOpen && "transform rotate-180"
          )} />
        </div>
      </button>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          <div
            className="px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm border-b border-gray-100"
            onClick={() => onYearChange(null)}
          >
            <span className="text-gray-700">Anos</span>
          </div>
          {isLoadingYears ? (
            <div className="px-3 py-2 text-sm text-gray-500">
              Carregando anos disponíveis...
            </div>
          ) : years.length === 0 ? (
            <div className="px-3 py-2 text-sm text-gray-500">
              Nenhum ano com dados encontrado
            </div>
          ) : (
            years.map((year) => (
              <div
                key={year}
                className={cn(
                  "px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm",
                  "flex items-center justify-between",
                  selectedYear === year && "bg-blue-50 text-blue-700"
                )}
                onClick={() => handleYearSelect(year)}
              >
                <span>{year}</span>
                {selectedYear === year && (
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                )}
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
};

export default YearFilter;
