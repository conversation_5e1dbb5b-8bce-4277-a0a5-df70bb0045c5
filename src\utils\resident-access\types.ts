
// Tipos para o sistema de acesso de moradores
export interface CreateResidentUserParams {
  email: string;
  password: string;
  name: string;
  apartment_id: string;
}

export interface CreateResidentUserResult {
  success: boolean;
  user_id: string;
  email: string;
  name: string;
  apartment_id: string;
  password?: string;
  message?: string;
}

export interface ResidentWithAccess {
  id: string;
  name: string;
  email: string;
  apartment_id: string;
  role: string;
  has_access: boolean;
  user_id?: string;
  access_granted_at?: string;
  access_granted_by?: string;
  first_login: boolean;
}
