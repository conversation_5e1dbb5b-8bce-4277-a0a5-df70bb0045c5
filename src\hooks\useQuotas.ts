
/**
 * Hook useQuotas - Gerenciamento de Quotas do Condomínio
 *
 * Hook personalizado para gerenciar todas as operações relacionadas às quotas,
 * incluindo criação, atualização, pagamento, exclusão e integração com fluxo de caixa.
 *
 * <AUTHOR> Prédio Azul
 * @version 1.0
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { Quota } from '@/types';
import {
  getQuotas,
  createQuota,
  updateQuota,
  markQuotaAsPaid,
  deleteQuota,
  regularizeFine
} from '@/utils/supabase-helpers';
import {
  generateMonthlyLancamento,
  updateLancamentoOnPayment,
  createFineEntry,
  associateLancamentoToQuotas,
  findOrCreateLancamento
} from '@/utils/quota-cashflow-integration';
import { validateSequentialPayment, validateSequentialFineRegularization } from '@/utils/quota-calculations';
import { useSettings } from '@/hooks/useSettings';
import { useNotifications } from '@/hooks/useNotifications';

interface UseQuotasOptions {
  onAddSuccess?: () => void;
  onEditSuccess?: () => void;
}

export function useQuotas(options?: UseQuotasOptions) {
  const queryClient = useQueryClient();
  const { getPaymentPeriodConfig } = useSettings();
  const { addNotification } = useNotifications();

  // Obter todas as quotas
  const { 
    data: quotas, 
    isLoading, 
    error, 
    refetch 
  } = useQuery({
    queryKey: ['quotas'],
    queryFn: getQuotas,
  });

  // Verifica se já existe uma quota para o morador, mês e ano específicos
  const checkQuotaExists = (moradorId: string, mes: number, ano: number): boolean => {
    if (!quotas) return false;
    
    return quotas.some(
      (quota) => 
        quota.morador_id === moradorId && 
        quota.mes === mes && 
        quota.ano === ano
    );
  };

  // Geração em massa de quotas SEM integração automática ao fluxo de caixa
  const { mutate: generateQuotas, isPending: isGeneratingQuotas } = useMutation({
    mutationFn: async (quotasToGenerate: Omit<Quota, 'id' | 'created_at' | 'updated_at'>[]) => {
      console.log('🔄 Hook: Iniciando geração em massa de quotas:', quotasToGenerate.length);

      const results = [];
      let successCount = 0;
      let skipCount = 0;
      let errorCount = 0;

      // Criar quotas individuais SEM lançamento no fluxo de caixa
      for (const quotaData of quotasToGenerate) {
        try {
          // Verifica se já existe
          if (checkQuotaExists(quotaData.morador_id, quotaData.mes, quotaData.ano)) {
            console.log(`⏭️ Quota já existe: Morador ${quotaData.morador_id}, ${quotaData.mes}/${quotaData.ano}`);
            skipCount++;
            continue;
          }

          // Criar quota SEM referência ao lançamento (será criado apenas no pagamento)
          const quotaWithoutLancamento = {
            ...quotaData,
            fluxo_caixa_id: null // Não associar lançamento na geração
          };

          const result = await createQuota(quotaWithoutLancamento);
          results.push(result);
          successCount++;
        } catch (error) {
          console.error('❌ Erro ao criar quota:', error);
          errorCount++;
        }
      }

      return { results, successCount, skipCount, errorCount };
    },
    onSuccess: ({ successCount, skipCount, errorCount }) => {
      queryClient.invalidateQueries({ queryKey: ['quotas'] });
      // Não invalidar fluxo de caixa pois não criamos lançamentos na geração

      if (errorCount === 0) {
        if (skipCount > 0) {
          toast.success(`${successCount} quotas geradas com sucesso! ${skipCount} já existiam. Lançamentos serão criados automaticamente no primeiro pagamento de cada mês.`);
        } else {
          toast.success(`${successCount} quotas geradas com sucesso! Lançamentos serão criados automaticamente no primeiro pagamento de cada mês.`);
        }
      } else {
        toast.warning(`Geração parcial: ${successCount} criadas, ${skipCount} já existiam, ${errorCount} com erro.`);
      }

      console.log('✅ Hook: Geração em massa concluída:', { successCount, skipCount, errorCount });
    },
    onError: (error) => {
      console.error('❌ Hook: Erro na geração em massa:', error);
      toast.error('Erro ao gerar quotas em massa.');
    },
  });

  // Adicionar nova quota
  const { mutate: addQuota, isPending: isAddingQuota } = useMutation({
    mutationFn: (newQuota: Omit<Quota, 'id' | 'created_at' | 'updated_at'>) => {
      console.log('🔄 Hook: Iniciando criação de quota:', newQuota);
      
      // Verifica se já existe uma quota para este morador no mesmo mês e ano
      if (checkQuotaExists(newQuota.morador_id, newQuota.mes, newQuota.ano)) {
        throw new Error('Este morador já possui uma quota registrada para o mês selecionado.');
      }
      
      // Convert empty string dates to null
      const processedQuota = {
        ...newQuota,
        data_pagamento: newQuota.data_pagamento === '' ? null : newQuota.data_pagamento
      };
      
      console.log('📝 Hook: Quota processada para criação:', processedQuota);
      return createQuota(processedQuota);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['quotas'] });
      toast.success('Quota adicionada com sucesso!');
      console.log('✅ Hook: Quota criada com sucesso:', data);

      // Adicionar notificação automática
      if (data && data.moradores) {
        const monthNames = [
          'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
          'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
        ];
        const monthName = monthNames[data.mes - 1];

        addNotification({
          type: 'quota',
          title: 'Nova quota registrada',
          description: `Quota de ${monthName}/${data.ano} foi registrada para o apartamento ${data.moradores.apartamento}`,
          relatedEntityType: 'quota',
          relatedEntityId: data.id
        });
      }

      options?.onAddSuccess?.();
    },
    onError: (error) => {
      console.error('❌ Hook: Erro ao adicionar quota:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao adicionar quota.');
    },
  });

  // Atualizar quota existente
  const { mutate: editQuota, isPending: isEditingQuota } = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Quota> }) => {
      console.log(`🔄 Hook: Iniciando edição da quota ${id}:`, data);
      
      // Verificar se está mudando para uma combinação morador/mês/ano que já existe
      if (
        data.morador_id && 
        typeof data.mes === 'number' && 
        typeof data.ano === 'number'
      ) {
        const currentQuota = quotas?.find(q => q.id === id);
        
        if (
          currentQuota && (
            data.morador_id !== currentQuota.morador_id ||
            data.mes !== currentQuota.mes ||
            data.ano !== currentQuota.ano
          )
        ) {
          const exists = checkQuotaExists(data.morador_id, data.mes, data.ano);
          if (exists) {
            throw new Error('Este morador já possui uma quota registrada para o mês selecionado.');
          }
        }
      }
      
      // Convert empty string dates to null
      const processedData = {
        ...data,
        data_pagamento: data.data_pagamento === '' ? null : data.data_pagamento
      };
      
      console.log(`📝 Hook: Dados processados para edição:`, processedData);
      return updateQuota(id, processedData);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['quotas'] });
      toast.success('Quota atualizada com sucesso!');
      console.log('✅ Hook: Quota atualizada com sucesso:', data);
      options?.onEditSuccess?.();
    },
    onError: (error) => {
      console.error('❌ Hook: Erro ao atualizar quota:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao atualizar quota.');
    },
  });

  // Marcar quota como paga - com integração correta ao fluxo de caixa
  const { mutate: setQuotaPaid } = useMutation({
    mutationFn: async ({ id, paymentDate }: { id: string; paymentDate: string }) => {
      console.log(`💳 Hook: Marcando quota ${id} como paga em ${paymentDate}`);

      // Buscar a quota para obter o valor e dados do mês
      const quotaAtual = quotas?.find(q => q.id === id);
      if (!quotaAtual) {
        throw new Error('Quota não encontrada');
      }

      // VALIDAÇÃO SEQUENCIAL: Verificar se pode pagar esta quota
      if (quotas && quotas.length > 0) {
        const quotasForValidation = quotas.map(q => ({
          id: q.id,
          morador_id: q.morador_id,
          mes: q.mes,
          ano: q.ano,
          status: q.status || 'Não Pago',
          multa: q.multa || 0,
          situacao: q.situacao || 'Não Regularizada',
          isento_quotas: q.moradores?.isento_quotas || false
        }));

        const validation = validateSequentialPayment(id, quotasForValidation);
        if (!validation.isValid) {
          throw new Error(validation.message || 'Erro na validação de pagamento sequencial');
        }
      }

      // Marcar quota como paga primeiro
      const result = await markQuotaAsPaid(id, paymentDate);

      if (result) {
        // Buscar ou criar lançamento para o mês e incrementar valor automaticamente
        const lancamentoId = await findOrCreateLancamento(quotaAtual.mes, quotaAtual.ano, quotaAtual.valor);

        if (lancamentoId) {
          // Associar o lançamento à quota (sempre necessário para manter referência)
          await associateLancamentoToQuotas([{
            morador_id: quotaAtual.morador_id,
            mes: quotaAtual.mes,
            ano: quotaAtual.ano,
            valor: quotaAtual.valor,
            data_vencimento: quotaAtual.data_vencimento,
            status: quotaAtual.status,
            multa: quotaAtual.multa,
            numero_multas: quotaAtual.numero_multas,
            situacao: quotaAtual.situacao
          }], lancamentoId);

          console.log('✅ Quota associada ao lançamento e valor incrementado automaticamente');
        } else {
          console.warn('⚠️ Quota marcada como paga, mas falha ao processar fluxo de caixa');
        }
      }

      return result;
    },
    onSuccess: (result, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['quotas'] });
      queryClient.invalidateQueries({ queryKey: ['fluxo-caixa'] });
      queryClient.invalidateQueries({ queryKey: ['saldo-total'] });
      toast.success('Quota marcada como paga e fluxo de caixa atualizado!');
      console.log('✅ Hook: Quota marcada como paga com integração ao fluxo de caixa');

      // Adicionar notificação de pagamento confirmado usando os dados retornados
      if (result && result.moradores) {
        const monthNames = [
          'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
          'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
        ];
        const monthName = monthNames[result.mes - 1];

        addNotification({
          type: 'payment',
          title: 'Pagamento confirmado',
          description: `Quota de ${monthName}/${result.ano} foi paga pelo apartamento ${result.moradores.apartamento}`,
          relatedEntityType: 'quota',
          relatedEntityId: result.id
        });
      }
    },
    onError: (error) => {
      console.error('❌ Hook: Erro ao marcar quota como paga:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao marcar quota como paga.');
    },
  });

  // Regularizar multa com criação de lançamento específico
  const { mutate: regularizeQuotaFine, isPending: isRegularizingFine } = useMutation({
    mutationFn: async (id: string) => {
      console.log(`🎯 Hook: Regularizando multa da quota ${id}`);
      
      // Buscar a quota para obter dados da multa
      const quotaAtual = quotas?.find(q => q.id === id);
      if (!quotaAtual) {
        throw new Error('Quota não encontrada');
      }

      if (quotaAtual.status !== 'Pago') {
        throw new Error('Não é possível regularizar multa de quota não paga');
      }

      if (quotaAtual.multa <= 0) {
        throw new Error('Esta quota não possui multa para regularizar');
      }

      // VALIDAÇÃO SEQUENCIAL: Verificar se pode regularizar esta multa
      if (quotas && quotas.length > 0) {
        const quotasForValidation = quotas.map(q => ({
          id: q.id,
          morador_id: q.morador_id,
          mes: q.mes,
          ano: q.ano,
          status: q.status || 'Não Pago',
          multa: q.multa || 0,
          situacao: q.situacao || 'Não Regularizada',
          isento_quotas: q.moradores?.isento_quotas || false
        }));

        const validation = validateSequentialFineRegularization(id, quotasForValidation);
        if (!validation.isValid) {
          throw new Error(validation.message || 'Erro na validação de regularização sequencial');
        }
      }

      // Regularizar a multa
      const result = await regularizeFine(id);
      
      if (result) {
        // Criar lançamento específico para a multa
        const fineEntrySuccess = await createFineEntry(quotaAtual, quotaAtual.multa);
        
        if (!fineEntrySuccess) {
          console.warn('⚠️ Multa regularizada, mas falha ao criar lançamento no fluxo de caixa');
        }
      }
      
      return result;
    },
    onSuccess: (result, id) => {
      queryClient.invalidateQueries({ queryKey: ['quotas'] });
      queryClient.invalidateQueries({ queryKey: ['fluxo-caixa'] });
      queryClient.invalidateQueries({ queryKey: ['saldo-total'] });
      toast.success('Multa regularizada e lançamento criado no fluxo de caixa!');
      console.log('✅ Hook: Multa regularizada com integração ao fluxo de caixa');

      // Adicionar notificação de multa regularizada
      const quotaMultada = quotas?.find(q => q.id === id);
      if (quotaMultada && quotaMultada.moradores) {
        const monthNames = [
          'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
          'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
        ];
        const monthName = monthNames[quotaMultada.mes - 1];

        addNotification({
          type: 'payment',
          title: 'Multa regularizada',
          description: `Multa da quota de ${monthName}/${quotaMultada.ano} foi regularizada para o apartamento ${quotaMultada.moradores.apartamento}`,
          relatedEntityType: 'quota',
          relatedEntityId: quotaMultada.id
        });
      }
    },
    onError: (error) => {
      console.error('❌ Hook: Erro ao regularizar multa:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao regularizar multa.');
    },
  });

  // Excluir quota (com logs melhorados)
  const { mutate: removeQuota, isPending: isDeletingQuota } = useMutation({
    mutationFn: (id: string) => {
      console.log(`🗑️ Hook: Iniciando exclusão da quota ${id}`);
      return deleteQuota(id);
    },
    onSuccess: (success) => {
      if (success) {
        queryClient.invalidateQueries({ queryKey: ['quotas'] });
        queryClient.invalidateQueries({ queryKey: ['fluxo-caixa'] });
        queryClient.invalidateQueries({ queryKey: ['saldo-total'] });
        toast.success('Quota excluída com sucesso!');
        console.log('✅ Hook: Quota excluída com sucesso');
      } else {
        toast.error('Falha ao excluir quota.');
        console.error('❌ Hook: Falha na exclusão da quota');
      }
    },
    onError: (error) => {
      console.error('❌ Hook: Erro ao excluir quota:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao excluir quota.');
    },
  });

  return {
    quotas,
    isLoading,
    isAddingQuota,
    isEditingQuota,
    isDeletingQuota,
    isRegularizingFine,
    isGeneratingQuotas,
    error,
    refetch,
    addQuota,
    editQuota,
    setQuotaPaid,
    regularizeQuotaFine,
    removeQuota,
    generateQuotas,
    checkQuotaExists,
    getPaymentPeriodConfig
  };
}
