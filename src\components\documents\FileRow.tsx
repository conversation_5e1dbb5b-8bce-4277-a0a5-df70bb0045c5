
import React from 'react';
import {
  FileIcon,
  FileTextIcon,
  ImageIcon,
  FolderIcon,
  Trash2Icon,
  DownloadIcon,
  ExternalLinkIcon,
  ShareIcon
} from 'lucide-react';
import { formatDistanceToNow, format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { TableRow, TableCell } from '@/components/ui/table';
import { DocumentFile } from '@/types';

interface FileRowProps {
  file: DocumentFile;
  onOpen: () => void;
  onDelete: () => void;
  onShare?: () => void;
  getFileUrl: (path: string) => string;
}

const FileRow: React.FC<FileRowProps> = ({ file, onOpen, onDelete, onShare, getFileUrl }) => {
  const getIcon = () => {
    if (file.isFolder) {
      return <FolderIcon className="w-5 h-5 text-blue-500" />;
    }
    
    const mimeType = file.metadata.mimetype;
    
    if (mimeType.includes('pdf')) {
      return <FileTextIcon className="w-5 h-5 text-red-500" />;
    } else if (mimeType.includes('image')) {
      return <ImageIcon className="w-5 h-5 text-green-500" />;
    } else {
      return <FileIcon className="w-5 h-5 text-gray-500" />;
    }
  };

  const fileSize = () => {
    if (file.isFolder) return '-';
    
    const size = file.metadata.size;
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
    return `${(size / (1024 * 1024)).toFixed(1)} MB`;
  };

  const createdAt = new Date(file.created_at);
  const formattedDate = format(createdAt, 'dd/MM/yyyy HH:mm');
  const relativeDate = formatDistanceToNow(createdAt, { 
    addSuffix: true,
    locale: ptBR
  });

  return (
    <TableRow 
      className="cursor-pointer hover:bg-gray-50"
      onClick={onOpen}
    >
      <TableCell>
        <div className="flex items-center">
          {getIcon()}
          <span className="ml-2">{file.name}</span>
        </div>
      </TableCell>
      <TableCell>{fileSize()}</TableCell>
      <TableCell title={formattedDate}>{relativeDate}</TableCell>
      <TableCell>
        <DropdownMenu>
          <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <span className="sr-only">Abrir menu</span>
              <span className="text-gray-500">•••</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={(e) => {
              e.stopPropagation();
              onOpen();
            }}>
              {file.isFolder ? (
                <>
                  <FolderIcon className="mr-2 h-4 w-4" />
                  <span>Abrir</span>
                </>
              ) : (
                <>
                  <ExternalLinkIcon className="mr-2 h-4 w-4" />
                  <span>Visualizar</span>
                </>
              )}
            </DropdownMenuItem>
            
            {!file.isFolder && (
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  window.open(getFileUrl(file.fullPath), '_blank');
                }}
              >
                <DownloadIcon className="mr-2 h-4 w-4" />
                <span>Baixar</span>
              </DropdownMenuItem>
            )}

            {!file.isFolder && onShare && (
              <DropdownMenuItem onClick={(e) => {
                e.stopPropagation();
                onShare();
              }}>
                <ShareIcon className="mr-2 h-4 w-4" />
                <span>Disponibilizar para Moradores</span>
              </DropdownMenuItem>
            )}

            <DropdownMenuSeparator />

            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
              className="text-red-600"
            >
              <Trash2Icon className="mr-2 h-4 w-4" />
              <span>Excluir</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  );
};

export default FileRow;
