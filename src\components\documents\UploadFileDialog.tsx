
import React, { useState, useRef } from 'react';
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { FileUpIcon, XIcon } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface UploadFileDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onUpload: (file: File) => void;
  allowedTypes?: string[];
  maxSize?: number; // in MB
}

const UploadFileDialog: React.FC<UploadFileDialogProps> = ({
  isOpen,
  onClose,
  onUpload,
  allowedTypes = ['application/pdf', 'image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  maxSize = 5 // 5MB default limit
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState<boolean>(false);
  const { toast } = useToast();
  const inputRef = useRef<HTMLInputElement | null>(null);

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const validateFile = (file: File): boolean => {
    console.log('=== VALIDAÇÃO DE ARQUIVO ===');
    console.log('Nome:', file.name);
    console.log('Tipo MIME:', file.type);
    console.log('Tamanho:', file.size, 'bytes');
    console.log('Tipos permitidos:', allowedTypes);

    // Verificar se o arquivo tem extensão
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    console.log('Extensão do arquivo:', fileExtension);

    // Check file type - incluir verificação por extensão para PDFs
    const isPdfByMime = file.type === 'application/pdf';
    const isPdfByExtension = fileExtension === 'pdf';
    const isImageByMime = file.type.startsWith('image/');
    const isWordByMime = file.type.includes('word') || file.type.includes('document');

    const isValidType = allowedTypes.includes(file.type) ||
                       (isPdfByExtension && (isPdfByMime || file.type === ''));

    console.log('Validação por MIME:', allowedTypes.includes(file.type));
    console.log('É PDF por MIME:', isPdfByMime);
    console.log('É PDF por extensão:', isPdfByExtension);
    console.log('É imagem:', isImageByMime);
    console.log('É Word:', isWordByMime);
    console.log('Tipo válido:', isValidType);

    if (!isValidType) {
      console.error('ERRO: Tipo de arquivo inválido');
      console.error('Tipo recebido:', file.type);
      console.error('Extensão:', fileExtension);
      toast({
        title: 'Tipo de arquivo inválido',
        description: `Apenas PDF, imagens (PNG, JPG, GIF, WebP) e documentos Word são permitidos. Tipo detectado: ${file.type || 'desconhecido'}`,
        variant: 'destructive'
      });
      return false;
    }

    // Check file size
    const fileSizeInMB = file.size / (1024 * 1024);
    console.log('Tamanho em MB:', fileSizeInMB);

    if (fileSizeInMB > maxSize) {
      console.error('ERRO: Arquivo muito grande');
      console.error('Tamanho:', fileSizeInMB, 'MB, máximo:', maxSize, 'MB');
      toast({
        title: 'Arquivo muito grande',
        description: `O tamanho máximo permitido é de ${maxSize}MB. Seu arquivo tem ${fileSizeInMB.toFixed(2)}MB.`,
        variant: 'destructive'
      });
      return false;
    }

    console.log('✅ VALIDAÇÃO PASSOU - Arquivo válido');
    return true;
  };

  const handleFileDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const newFile = e.dataTransfer.files[0];
      if (validateFile(newFile)) {
        setFile(newFile);
      }
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFile = e.target.files[0];
      if (validateFile(newFile)) {
        setFile(newFile);
      }
    }
  };

  const handleUpload = () => {
    if (file) {
      console.log('=== INICIANDO UPLOAD ===');
      console.log('Arquivo selecionado:', file.name);
      console.log('Tipo MIME:', file.type);
      console.log('Tamanho:', file.size, 'bytes');

      try {
        onUpload(file);
        console.log('✅ Upload iniciado com sucesso');
        handleReset();
      } catch (error) {
        console.error('❌ ERRO no upload:', error);
      }
    } else {
      console.error('❌ ERRO: Nenhum arquivo selecionado para upload');
    }
  };

  const handleReset = () => {
    setFile(null);
    if (inputRef.current) {
      inputRef.current.value = '';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Enviar Arquivo</DialogTitle>
          <DialogDescription>
            Selecione um arquivo para fazer upload.
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div 
            className={`border-2 border-dashed p-6 rounded-lg text-center ${
              dragActive ? 'border-primary bg-primary/5' : 'border-gray-300'
            }`}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleFileDrop}
          >
            {!file ? (
              <>
                <FileUpIcon className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-4">
                  <p className="text-sm text-gray-600">
                    Arraste um arquivo ou clique para selecionar
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    PDF, imagens (PNG, JPG, GIF, WebP) ou documentos Word até {maxSize}MB
                  </p>
                </div>
                <Label htmlFor="file-upload" className="sr-only">
                  Escolher arquivo
                </Label>
                <Input
                  id="file-upload"
                  ref={inputRef}
                  type="file"
                  accept={allowedTypes.join(',')}
                  onChange={handleFileChange}
                  className="hidden"
                />
                <Button
                  variant="outline"
                  onClick={() => inputRef.current?.click()}
                  className="mt-4"
                >
                  Selecionar arquivo
                </Button>
              </>
            ) : (
              <div className="flex items-center justify-between bg-gray-50 p-3 rounded">
                <div className="flex items-center">
                  <FileUpIcon className="h-6 w-6 text-primary mr-3" />
                  <div>
                    <p className="text-sm font-medium truncate max-w-[200px]">
                      {file.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {file.size < 1024 * 1024
                        ? `${(file.size / 1024).toFixed(1)} KB`
                        : `${(file.size / (1024 * 1024)).toFixed(1)} MB`}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleReset}
                >
                  <XIcon className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>
        </div>
        
        <DialogFooter className="flex justify-between sm:justify-between">
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button 
            type="submit" 
            onClick={handleUpload} 
            disabled={!file}
          >
            Enviar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default UploadFileDialog;
