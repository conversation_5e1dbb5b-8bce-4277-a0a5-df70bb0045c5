import { useMemo } from 'react';
import { <PERSON>uo<PERSON>, Morador } from '@/types';

interface QuotasSummaryFilters {
  selectedMorador: Morador | null;
  selectedStatus: string | null;
  selectedFineStatus: string | null;
  selectedMonth: number | null;
  selectedYear: number | null;
}

interface QuotasSummary {
  totalQuotaAmount: number;
  totalFinesAmount: number;
  combinedTotal: number;
  quotaCount: number;
  fineCount: number;
}

export const useQuotasSummary = (
  quotas: Quota[] | undefined,
  filters: QuotasSummaryFilters
): QuotasSummary => {
  return useMemo(() => {
    if (!quotas || quotas.length === 0) {
      return {
        totalQuotaAmount: 0,
        totalFinesAmount: 0,
        combinedTotal: 0,
        quotaCount: 0,
        fineCount: 0
      };
    }

    // Apply the same filtering logic as the main page
    let filteredQuotas = quotas;

    // Filter by resident
    if (filters.selectedMorador) {
      filteredQuotas = filteredQuotas.filter(quota =>
        quota.morador_id === filters.selectedMorador!.id
      );
    }

    // Filter by status
    if (filters.selectedStatus) {
      filteredQuotas = filteredQuotas.filter(quota =>
        quota.status === filters.selectedStatus
      );
    }

    // Filter by fine status
    if (filters.selectedFineStatus) {
      if (filters.selectedFineStatus === 'Regularizada') {
        filteredQuotas = filteredQuotas.filter(quota =>
          quota.multa > 0 && quota.situacao === 'Regularizada'
        );
      } else if (filters.selectedFineStatus === 'Não Regularizada') {
        filteredQuotas = filteredQuotas.filter(quota =>
          quota.multa > 0 && quota.situacao === 'Não Regularizada'
        );
      }
    }

    // Filter by month
    if (filters.selectedMonth) {
      filteredQuotas = filteredQuotas.filter(quota =>
        quota.mes === filters.selectedMonth
      );
    }

    // Filter by year
    if (filters.selectedYear) {
      filteredQuotas = filteredQuotas.filter(quota =>
        quota.ano === filters.selectedYear
      );
    }

    // Calculate totals - only count unpaid quotas and unpaid fines
    const unpaidQuotas = filteredQuotas.filter(quota => quota.status !== 'Pago');
    const totalQuotaAmount = unpaidQuotas.reduce((sum, quota) => sum + quota.valor, 0);

    // Only count fines that are not regularized (unpaid fines)
    const totalFinesAmount = filteredQuotas
      .filter(quota => quota.multa > 0 && quota.situacao !== 'Regularizada')
      .reduce((sum, quota) => sum + quota.multa, 0);

    const combinedTotal = totalQuotaAmount + totalFinesAmount;
    const quotaCount = filteredQuotas.length;

    // Count only unpaid fines
    const fineCount = filteredQuotas.filter(quota =>
      quota.multa > 0 && quota.situacao !== 'Regularizada'
    ).length;

    return {
      totalQuotaAmount,
      totalFinesAmount,
      combinedTotal,
      quotaCount,
      fineCount
    };
  }, [quotas, filters.selectedMorador, filters.selectedStatus, filters.selectedFineStatus, filters.selectedMonth, filters.selectedYear]);
};
