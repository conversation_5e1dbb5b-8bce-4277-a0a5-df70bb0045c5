
import React from 'react';
import { QuotaTableControls } from '@/components/quotas/QuotaTableControls';
import { Morador } from '@/types';

interface QuotasFiltersSectionProps {
  moradores: Morador[];
  selectedMorador: Morador | null;
  onMoradorChange: (morador: Morador | null) => void;
  selectedStatus: string | null;
  onStatusChange: (status: string | null) => void;
  selectedFineStatus: string | null;
  onFineStatusChange: (status: string | null) => void;
  selectedMonth: number | null;
  onMonthChange: (month: number | null) => void;
  selectedYear: number | null;
  onYearChange: (year: number | null) => void;
}

const QuotasFiltersSection: React.FC<QuotasFiltersSectionProps> = ({
  moradores,
  selectedMorador,
  onMoradorChange,
  selectedStatus,
  onStatusChange,
  selectedFineStatus,
  onFineStatusChange,
  selectedMonth,
  onMonth<PERSON>hange,
  selectedYear,
  onYearChange
}) => {
  return (
    <div className="mt-6 animate-enter">
      <QuotaTableControls
        moradores={moradores}
        selectedMorador={selectedMorador}
        onMoradorChange={onMoradorChange}
        selectedStatus={selectedStatus}
        onStatusChange={onStatusChange}
        selectedFineStatus={selectedFineStatus}
        onFineStatusChange={onFineStatusChange}
        selectedMonth={selectedMonth}
        onMonthChange={onMonthChange}
        selectedYear={selectedYear}
        onYearChange={onYearChange}
      />
    </div>
  );
};

export default QuotasFiltersSection;
