
import { supabase } from '@/integrations/supabase/client';
import { ResidentWithAccess } from './types';

/**
 * Busca todos os moradores com status de acesso
 */
export const getResidentsWithAccessStatus = async (): Promise<ResidentWithAccess[]> => {
  try {
    const { data, error } = await supabase.rpc('get_residents_with_access_status');

    if (error) {
      throw error;
    }

    // Mapear dados da função RPC para o tipo esperado
    return (data || []).map((item: any) => ({
      id: item.morador_id,
      name: item.morador_nome,
      email: item.morador_email,
      apartment_id: item.apartamento,
      role: 'user', // Default role for residents
      has_access: item.has_access,
      user_id: item.user_id,
      access_granted_at: item.access_granted_at,
      first_login: item.first_login
    }));
  } catch (error) {
    return [];
  }
};

export const revokeResidentAccess = async (userId: string, apartmentId: string): Promise<boolean> => {
  try {
    console.log('🗑️ Removing resident completely:', { userId, apartmentId });

    const { data, error } = await supabase.rpc('remove_resident_completely', {
      p_user_id: userId,
      p_apartment_id: apartmentId
    });

    if (error) {
      console.error('❌ Error in remove_resident_completely:', error);
      throw error;
    }

    // Verificar se data existe e fazer cast para objeto
    if (!data) {
      throw new Error('No response from function');
    }

    // Fazer cast seguro do tipo Json para objeto
    const result = data as { success?: boolean; error?: string };

    if (!result.success) {
      console.error('❌ Function returned error:', result.error);
      throw new Error(result.error || 'Failed to remove resident completely');
    }

    console.log('✅ Resident removed completely:', result);
    return true;
  } catch (error) {
    console.error('❌ Error in revokeResidentAccess:', error);
    throw error;
  }
};
