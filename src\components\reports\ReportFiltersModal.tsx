/**
 * Modal de Filtros para Relatórios - Sistema Prédio Azul
 * 
 * Interface intuitiva para configurar filtros antes da geração
 * de relatórios, com opções específicas para cada tipo.
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CalendarIcon, Filter, Download } from 'lucide-react';
import { format, parse, isValid } from 'date-fns';
import { pt } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import {
  ReportType,
  ReportFilters,
  QuotasReportFilters,
  ResidentsReportFilters,
  FinancialReportFilters,
  CommitteeReportFilters,
  FinesReportFilters
} from '@/types/reports';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { getConfiguracaoByNome } from '@/utils/supabase-helpers';

interface ReportFiltersModalProps {
  isOpen: boolean;
  onClose: () => void;
  onGenerate: (filters: ReportFilters) => void;
  reportType: ReportType;
  isGenerating?: boolean;
}

/**
 * Componente principal do modal de filtros
 */
export const ReportFiltersModal: React.FC<ReportFiltersModalProps> = ({
  isOpen,
  onClose,
  onGenerate,
  reportType,
  isGenerating = false
}) => {
  // Estados para filtros base
  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();
  const [startDateInput, setStartDateInput] = useState<string>('');
  const [endDateInput, setEndDateInput] = useState<string>('');
  const [periodPreset, setPeriodPreset] = useState<string>('current_month');

  // Estados específicos para quotas
  const [quotaStatus, setQuotaStatus] = useState<string>('all');
  const [apartmentNumber, setApartmentNumber] = useState('all');
  const [includeExempt, setIncludeExempt] = useState(false);

  // Estados específicos para moradores
  const [residentStatus, setResidentStatus] = useState<string>('all');
  const [includeContact, setIncludeContact] = useState(false);

  // Estados específicos para financeiro
  const [financialType, setFinancialType] = useState<string>('all');
  const [category, setCategory] = useState('');
  const [includeBalance, setIncludeBalance] = useState(true);
  const [groupByMonth, setGroupByMonth] = useState(false);

  // Estados específicos para comissão
  const [includeHistory, setIncludeHistory] = useState(false);

  // Initialize input fields when dates change
  useEffect(() => {
    if (startDate) {
      setStartDateInput(format(startDate, 'dd/MM/yyyy'));
    }
  }, [startDate]);

  useEffect(() => {
    if (endDate) {
      setEndDateInput(format(endDate, 'dd/MM/yyyy'));
    }
  }, [endDate]);

  // Initialize on modal open
  useEffect(() => {
    if (isOpen && !startDate && !endDate) {
      resetFilters();
    }
  }, [isOpen]);

  // Query para buscar apartamentos únicos
  const { data: apartamentos = [] } = useQuery({
    queryKey: ['apartamentos-unicos'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('moradores')
        .select('apartamento')
        .order('apartamento', { ascending: true });

      if (error) throw error;

      // Extrair apartamentos únicos e filtrar valores válidos
      const validApartments = data
        ?.map(m => m.apartamento)
        .filter(apt => apt && apt.toString().trim() !== '') || [];

      const uniqueApartments = [...new Set(validApartments)];
      return uniqueApartments.sort((a, b) => {
        // Ordenação numérica para apartamentos
        const numA = parseInt(a);
        const numB = parseInt(b);
        if (!isNaN(numA) && !isNaN(numB)) {
          return numA - numB;
        }
        return a.localeCompare(b);
      });
    },
    enabled: isOpen // Só busca quando o modal está aberto
  });

  // Query para buscar categorias das configurações
  const { data: categoriesConfig } = useQuery({
    queryKey: ['categories-config'],
    queryFn: () => getConfiguracaoByNome('categorias'),
    enabled: isOpen && reportType === 'financial'
  });

  // Processar categorias
  const availableCategories = categoriesConfig
    ? categoriesConfig.split(',').map(cat => cat.trim()).filter(Boolean)
    : [];

  /**
   * Reseta filtros quando o tipo de relatório muda
   */
  useEffect(() => {
    if (isOpen) {
      resetFilters();
    }
  }, [reportType, isOpen]);

  /**
   * Reseta todos os filtros para valores padrão
   */
  // Handlers for manual date input
  const handleStartDateInputChange = (value: string) => {
    setStartDateInput(value);
    const parsedDate = parse(value, 'dd/MM/yyyy', new Date());
    if (isValid(parsedDate)) {
      setStartDate(parsedDate);
    }
  };

  const handleEndDateInputChange = (value: string) => {
    setEndDateInput(value);
    const parsedDate = parse(value, 'dd/MM/yyyy', new Date());
    if (isValid(parsedDate)) {
      setEndDate(parsedDate);
    }
  };

  const handleStartDateSelect = (date: Date | undefined) => {
    if (date) {
      setStartDate(date);
      setStartDateInput(format(date, 'dd/MM/yyyy'));
    }
  };

  const handleEndDateSelect = (date: Date | undefined) => {
    if (date) {
      setEndDate(date);
      setEndDateInput(format(date, 'dd/MM/yyyy'));
    }
  };

  const resetFilters = () => {
    // Definir mês atual como padrão
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();

    const startOfMonth = new Date(currentYear, currentMonth, 1);
    const endOfMonth = new Date(currentYear, currentMonth + 1, 0);

    setStartDate(startOfMonth);
    setEndDate(endOfMonth);
    setStartDateInput(format(startOfMonth, 'dd/MM/yyyy'));
    setEndDateInput(format(endOfMonth, 'dd/MM/yyyy'));
    setPeriodPreset('current_month');
    setQuotaStatus('all');
    setApartmentNumber('all');
    setIncludeExempt(false);
    setResidentStatus('all');
    setIncludeContact(false);
    setFinancialType('all');
    setCategory('');
    setIncludeBalance(true);
    setGroupByMonth(false);
    setIncludeHistory(false);
  };

  /**
   * Aplica preset de período
   */
  const applyPeriodPreset = (preset: string) => {
    setPeriodPreset(preset);
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();

    let newStartDate: Date;
    let newEndDate: Date;

    switch (preset) {
      case 'current_month':
        newStartDate = new Date(currentYear, currentMonth, 1);
        newEndDate = new Date(currentYear, currentMonth + 1, 0);
        break;
      case 'last_month':
        newStartDate = new Date(currentYear, currentMonth - 1, 1);
        newEndDate = new Date(currentYear, currentMonth, 0);
        break;
      case 'current_year':
        newStartDate = new Date(currentYear, 0, 1);
        newEndDate = new Date(currentYear, 11, 31);
        break;
      case 'last_year':
        newStartDate = new Date(currentYear - 1, 0, 1);
        newEndDate = new Date(currentYear - 1, 11, 31);
        break;
      case 'custom':
        // Não altera as datas para permitir seleção manual
        return;
    }

    setStartDate(newStartDate);
    setEndDate(newEndDate);
    setStartDateInput(format(newStartDate, 'dd/MM/yyyy'));
    setEndDateInput(format(newEndDate, 'dd/MM/yyyy'));
  };

  /**
   * Constrói objeto de filtros baseado no tipo de relatório
   */
  const buildFilters = (): ReportFilters => {
    const baseFilters = {
      startDate: startDate?.toISOString().split('T')[0],
      endDate: endDate?.toISOString().split('T')[0]
    };

    switch (reportType) {
      case 'quotas':
        return {
          ...baseFilters,
          status: quotaStatus as any,
          apartmentNumber: apartmentNumber === 'all' ? undefined : apartmentNumber,
          includeExempt
        } as QuotasReportFilters;

      case 'residents':
        return {
          ...baseFilters,
          status: residentStatus as any,
          apartmentNumber: apartmentNumber === 'all' ? undefined : apartmentNumber,
          includeContact,
          includeExempt
        } as ResidentsReportFilters;

      case 'financial':
        return {
          ...baseFilters,
          type: financialType as any,
          category: category === 'all' ? undefined : category,
          includeBalance,
          groupByMonth
        } as FinancialReportFilters;

      case 'committee':
        return {
          ...baseFilters,
          includeContact
        } as CommitteeReportFilters;

      case 'fines':
        return {
          ...baseFilters,
          status: quotaStatus as any,
          apartmentNumber: apartmentNumber === 'all' ? undefined : apartmentNumber
        } as FinesReportFilters;

      default:
        return baseFilters;
    }
  };

  /**
   * Manipula a geração do relatório
   */
  const handleGenerate = () => {
    const filters = buildFilters();
    onGenerate(filters);
  };

  /**
   * Obtém título do modal baseado no tipo de relatório
   */
  const getModalTitle = () => {
    const titles = {
      quotas: 'Filtros - Relatório de Quotas',
      residents: 'Filtros - Relatório de Moradores',
      financial: 'Filtros - Relatório Financeiro',
      committee: 'Filtros - Relatório da Comissão',
      fines: 'Filtros - Relatório de Multas'
    };
    return titles[reportType] || 'Filtros do Relatório';
  };

  /**
   * Obtém descrição do modal baseado no tipo de relatório
   */
  const getModalDescription = () => {
    const descriptions = {
      quotas: 'Configure os filtros para gerar o relatório de quotas com os dados desejados.',
      residents: 'Configure os filtros para gerar o relatório de moradores com as informações necessárias.',
      financial: 'Configure os filtros para gerar o relatório financeiro com o período e categorias desejadas.',
      committee: 'Configure os filtros para gerar o relatório da comissão com as informações dos membros.',
      fines: 'Configure os filtros para gerar o relatório de multas com os dados desejados.'
    };
    return descriptions[reportType] || 'Configure os filtros para o relatório.';
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-primary" />
            {getModalTitle()}
          </DialogTitle>
          <DialogDescription>
            {getModalDescription()}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Seleção de Período - Apenas para relatórios que não sejam de moradores ou comissão */}
          {reportType !== 'residents' && reportType !== 'committee' && (
            <>
              <div className="space-y-4">
                <Label>Período do Relatório</Label>
                <Select value={periodPreset} onValueChange={applyPeriodPreset}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecionar período" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="current_month">Mês Atual</SelectItem>
                    <SelectItem value="last_month">Mês Passado</SelectItem>
                    <SelectItem value="current_year">Ano Atual</SelectItem>
                    <SelectItem value="last_year">Ano Passado</SelectItem>
                    <SelectItem value="custom">Período Personalizado</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Filtros de Data - Apenas quando período personalizado */}
              {periodPreset === 'custom' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="start-date">Data Inicial</Label>

                  {/* Manual Date Input */}
                  <Input
                    type="text"
                    placeholder="dd/mm/aaaa"
                    value={startDateInput}
                    onChange={(e) => handleStartDateInputChange(e.target.value)}
                    className="w-full"
                  />

                  {/* Calendar Picker */}
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-muted-foreground">ou selecione no calendário:</span>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 w-8 p-0"
                        >
                          <CalendarIcon className="h-4 w-4" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-auto p-0"
                        align="center"
                        side="bottom"
                        sideOffset={8}
                        avoidCollisions={true}
                        collisionPadding={16}
                      >
                        <Calendar
                          mode="single"
                          selected={startDate}
                          onSelect={handleStartDateSelect}
                          initialFocus
                          locale={pt}
                          className="pointer-events-auto"
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="end-date">Data Final</Label>

                  {/* Manual Date Input */}
                  <Input
                    type="text"
                    placeholder="dd/mm/aaaa"
                    value={endDateInput}
                    onChange={(e) => handleEndDateInputChange(e.target.value)}
                    className="w-full"
                  />

                  {/* Calendar Picker */}
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-muted-foreground">ou selecione no calendário:</span>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 w-8 p-0"
                        >
                          <CalendarIcon className="h-4 w-4" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-auto p-0"
                        align="center"
                        side="bottom"
                        sideOffset={8}
                        avoidCollisions={true}
                        collisionPadding={16}
                      >
                        <Calendar
                          mode="single"
                          selected={endDate}
                          onSelect={handleEndDateSelect}
                          initialFocus
                          locale={pt}
                          className="pointer-events-auto"
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </div>
              )}
            </>
          )}

          {/* Filtros Específicos por Tipo de Relatório */}
          {reportType === 'quotas' && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Status das Quotas</Label>
                  <Select value={quotaStatus} onValueChange={setQuotaStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecionar status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todas</SelectItem>
                      <SelectItem value="paid">Pagas</SelectItem>
                      <SelectItem value="pending">Pendentes</SelectItem>
                      <SelectItem value="overdue">Em Atraso</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Apartamento (Opcional)</Label>
                  <Select value={apartmentNumber} onValueChange={setApartmentNumber}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecionar apartamento" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos os apartamentos</SelectItem>
                      {apartamentos
                        .filter(apt => apt && apt.toString().trim() !== '')
                        .map((apt) => (
                          <SelectItem key={apt} value={apt.toString()}>
                            Apartamento {apt}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Incluir Moradores Isentos</Label>
                  <p className="text-sm text-muted-foreground">
                    Incluir quotas de moradores com isenção
                  </p>
                </div>
                <Switch
                  checked={includeExempt}
                  onCheckedChange={setIncludeExempt}
                />
              </div>


            </div>
          )}

          {reportType === 'residents' && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Status dos Moradores</Label>
                  <Select value={residentStatus} onValueChange={setResidentStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecionar status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos</SelectItem>
                      <SelectItem value="active">Ativos</SelectItem>
                      <SelectItem value="inactive">Inativos</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Apartamento (Opcional)</Label>
                  <Select value={apartmentNumber} onValueChange={setApartmentNumber}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecionar apartamento" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos os apartamentos</SelectItem>
                      {apartamentos
                        .filter(apt => apt && apt.toString().trim() !== '')
                        .map((apt) => (
                          <SelectItem key={apt} value={apt.toString()}>
                            Apartamento {apt}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Incluir Informações de Contato</Label>
                  <p className="text-sm text-muted-foreground">
                    Incluir telefone e email no relatório
                  </p>
                </div>
                <Switch
                  checked={includeContact}
                  onCheckedChange={setIncludeContact}
                />
              </div>
            </div>
          )}

          {reportType === 'financial' && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Tipo de Transação</Label>
                  <Select value={financialType} onValueChange={setFinancialType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecionar tipo" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todas</SelectItem>
                      <SelectItem value="income">Entradas</SelectItem>
                      <SelectItem value="expense">Saídas</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Categoria (Opcional)</Label>
                  <Select value={category} onValueChange={setCategory}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecionar categoria" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todas as categorias</SelectItem>
                      {availableCategories.map((cat) => (
                        <SelectItem key={cat} value={cat}>
                          {cat}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Incluir Saldo</Label>
                  <p className="text-sm text-muted-foreground">
                    Mostrar saldo atual no relatório
                  </p>
                </div>
                <Switch
                  checked={includeBalance}
                  onCheckedChange={setIncludeBalance}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Agrupar por Mês</Label>
                  <p className="text-sm text-muted-foreground">
                    Incluir quebra mensal no relatório
                  </p>
                </div>
                <Switch
                  checked={groupByMonth}
                  onCheckedChange={setGroupByMonth}
                />
              </div>
            </div>
          )}

          {reportType === 'committee' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Incluir Informações de Contato</Label>
                  <p className="text-sm text-muted-foreground">
                    Incluir telefone e email dos membros
                  </p>
                </div>
                <Switch
                  checked={includeContact}
                  onCheckedChange={setIncludeContact}
                />
              </div>
            </div>
          )}

          {reportType === 'fines' && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Status das Multas</Label>
                  <Select value={quotaStatus} onValueChange={setQuotaStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecionar status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todas</SelectItem>
                      <SelectItem value="paid">Pagas</SelectItem>
                      <SelectItem value="pending">Pendentes</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Apartamento (Opcional)</Label>
                  <Select value={apartmentNumber} onValueChange={setApartmentNumber}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecionar apartamento" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos os apartamentos</SelectItem>
                      {apartamentos
                        .filter(apt => apt && apt.toString().trim() !== '')
                        .map((apt) => (
                          <SelectItem key={apt} value={apt.toString()}>
                            Apartamento {apt}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}


        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isGenerating}>
            Cancelar
          </Button>
          <Button onClick={handleGenerate} disabled={isGenerating}>
            <Download className="mr-2 h-4 w-4" />
            {isGenerating ? 'Gerando...' : 'Gerar Relatório'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
