
/**
 * Componente DashboardCard - Card de Estatística do Dashboard
 *
 * Exibe uma métrica específica do dashboard com ícone, valor, descrição
 * e indicador de tendência opcional. Inclui estado de carregamento.
 *
 * <AUTHOR> Prédio Azul
 * @version 1.0
 */

import React from 'react';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';

/**
 * Interface para as propriedades do componente DashboardCard
 */
interface DashboardCardProps {
  title: string;                    // Título da métrica (ex: "Quotas em Atraso")
  value: string | number;           // Valor principal da métrica
  description?: string;             // Descrição adicional (ex: "vs. mês anterior")
  icon?: LucideIcon;                // Ícone do Lucide React (opcional)
  iconBgColor?: string;             // Cor de fundo do ícone (classe Tailwind)
  iconColor?: string;               // Cor do ícone (classe Tailwind)
  trend?: {                         // Indicador de tendência opcional
    value: number;                  // Valor da tendência (percentual)
    isPositive: boolean;            // Se a tendência é positiva ou negativa
  };
  className?: string;               // Classes CSS adicionais
  hoverEffect?: boolean;            // Se deve ter efeito hover
  onClick?: () => void;             // Função de clique (torna o card clicável)
  isLoading?: boolean;              // Estado de carregamento
}

/**
 * Componente funcional DashboardCard
 *
 * Renderiza um card de estatística com suporte a carregamento,
 * tendências e interatividade.
 *
 * @param props - Propriedades do componente
 * @returns {JSX.Element} Card de estatística renderizado
 */
const DashboardCard: React.FC<DashboardCardProps> = ({
  title,                            // Título da métrica
  value,                            // Valor principal
  description,                      // Descrição adicional
  icon: Icon,                       // Ícone (renomeado para Icon)
  iconBgColor = 'bg-primary-100',   // Cor de fundo padrão do ícone
  iconColor = 'text-primary-500',   // Cor padrão do ícone
  trend,                            // Dados de tendência
  className,                        // Classes CSS extras
  hoverEffect = true,               // Efeito hover habilitado por padrão
  onClick,                          // Função de clique
  isLoading = false,                // Estado de carregamento
}) => {
  // Renderização do Skeleton durante carregamento
  if (isLoading) {
    return (
      <div className={cn(
        "dashboard-card flex flex-col p-5 rounded-lg bg-white shadow-sm",
        className
      )}>
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1">
            <div className="animate-pulse bg-gray-200 rounded h-4 w-24 mb-2" />
            <div className="animate-pulse bg-gray-200 rounded h-8 w-20" />
          </div>
          <div className="animate-pulse bg-gray-200 rounded-lg h-12 w-12" />
        </div>
        <div className="flex items-center mt-1">
          <div className="animate-pulse bg-gray-200 rounded h-3 w-12 mr-2" />
          <div className="animate-pulse bg-gray-200 rounded h-3 w-32" />
        </div>
      </div>
    );
  }
  // Renderização principal do card
  return (
    <div
      className={cn(
        "dashboard-card flex flex-col p-5 rounded-lg bg-white shadow-sm",
        hoverEffect && "hover:shadow-md transition-shadow duration-300",  // Efeito hover condicional
        onClick && "cursor-pointer",                                      // Cursor pointer se clicável
        className                                                         // Classes adicionais
      )}
      onClick={onClick}
    >
      {/* Seção superior: Título, valor e ícone */}
      <div className="flex justify-between items-start mb-4">
        {/* Lado esquerdo: Título e valor */}
        <div>
          <h3 className="text-sm font-medium text-gray-500">{title}</h3>
          <p className="text-xl font-bold text-gray-800 mt-1" style={{ fontSize: '1.2rem' }}>{value}</p>
        </div>

        {/* Lado direito: Ícone (se fornecido) */}
        {Icon && (
          <div className={cn("p-3 rounded-lg", iconBgColor)}>
            <Icon className={cn("h-6 w-6", iconColor)} />
          </div>
        )}
      </div>

      {/* Seção inferior: Tendência e descrição (se existirem) */}
      {(description || trend) && (
        <div className="flex items-center mt-1">
          {/* Indicador de tendência */}
          {trend && (
            <div className={cn(
              "flex items-center text-xs font-medium mr-2",
              trend.isPositive ? "text-green-600" : "text-red-600"  // Cor baseada na tendência
            )}>
              <span className="mr-1">{trend.isPositive ? '↑' : '↓'}</span>
              <span>{Math.round(Math.abs(trend.value))}%</span>
            </div>
          )}

          {/* Descrição adicional */}
          {description && (
            <p className="text-xs text-gray-500">{description}</p>
          )}
        </div>
      )}
    </div>
  );
};

export default DashboardCard;
