
import { supabase } from '@/integrations/supabase/client';
import { CreateResidentUserParams, CreateResidentUserResult } from './types';
import { checkEmailExists } from './email-validation';

/**
 * Cria um usuário morador com acesso ao sistema usando Edge Function
 */
export const createResidentUser = async (params: CreateResidentUserParams): Promise<CreateResidentUserResult> => {
  return await createResidentUserViaEdgeFunction(params);
};

/**
 * Método usando Edge Function
 */
const createResidentUserViaEdgeFunction = async (params: CreateResidentUserParams): Promise<CreateResidentUserResult> => {
  try {
    // Validação preventiva: verificar se email já existe
    const emailExists = await checkEmailExists(params.email);
    if (emailExists) {
      throw new Error('Este email já está registado no sistema');
    }

    // Verificar se o utilizador está autenticado
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (!session || !session.user) {
      throw new Error('Sessão não encontrada. Por favor, faça login novamente.');
    }

    // Verificar se o token está válido
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      throw new Error('Token inválido. Por favor, faça login novamente.');
    }

    // Validação rigorosa dos parâmetros antes de enviar
    if (!params.email || typeof params.email !== 'string' || !params.email.trim()) {
      throw new Error('Email é obrigatório e deve ser uma string válida');
    }
    
    if (!params.password || typeof params.password !== 'string' || params.password.length < 6) {
      throw new Error('Password é obrigatória e deve ter pelo menos 6 caracteres');
    }
    
    if (!params.name || typeof params.name !== 'string' || !params.name.trim()) {
      throw new Error('Nome é obrigatório e deve ser uma string válida');
    }
    
    if (!params.apartment_id || typeof params.apartment_id !== 'string' || !params.apartment_id.trim()) {
      throw new Error('Apartment ID é obrigatório e deve ser uma string válida');
    }

    // Preparar payload com validação rigorosa
    const payload = {
      email: String(params.email).trim().toLowerCase(),
      password: String(params.password),
      name: String(params.name).trim(),
      apartment_id: String(params.apartment_id).trim().toUpperCase()
    };

    // Verificar se o payload pode ser serializado correctamente
    try {
      JSON.stringify(payload);
    } catch (serializationError) {
      throw new Error('Erro na serialização dos dados. Verifique os caracteres especiais.');
    }

    // Debug temporário para verificar o payload
    console.log('🔍 [DEBUG] Payload being sent to Edge Function:', payload);
    console.log('🔍 [DEBUG] Payload JSON:', JSON.stringify(payload));

    // Tentar com fetch direto para ter mais controle
    const supabaseUrl = 'https://qrcegsdhbwgjtkebqjok.supabase.co';
    const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFyY2Vnc2RoYndnanRrZWJxam9rIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDI5MTIzNDMsImV4cCI6MjA1ODQ4ODM0M30.r94EU08552kYer4TIGBxlg4h-dkqXt6_tOb1wzSm_lc';
    const functionUrl = `${supabaseUrl}/functions/v1/create-resident-user`;

    console.log('🔍 [DEBUG] Function URL:', functionUrl);
    console.log('🔍 [DEBUG] Session token:', session.access_token?.substring(0, 20) + '...');

    const fetchResponse = await fetch(functionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
        'apikey': supabaseAnonKey
      },
      body: JSON.stringify(payload)
    });

    console.log('🔍 [DEBUG] Fetch response status:', fetchResponse.status);
    console.log('🔍 [DEBUG] Fetch response headers:', Object.fromEntries(fetchResponse.headers.entries()));

    const responseText = await fetchResponse.text();
    console.log('🔍 [DEBUG] Raw response text:', responseText);

    let responseData: any;
    let responseError: any = null;

    if (!fetchResponse.ok) {
      responseError = {
        status: fetchResponse.status,
        statusText: fetchResponse.statusText,
        message: responseText
      };
    } else {
      try {
        responseData = JSON.parse(responseText);
      } catch (parseError) {
        responseError = {
          status: 500,
          message: 'Failed to parse response JSON'
        };
      }
    }

    const response = { data: responseData, error: responseError };

    // Verificar se houve erro na chamada da função
    if (response.error) {
      const errorStatus = response.error.status || response.error.code || 500;
      const errorMessage = response.error.message || response.error.error || response.error.statusText || 'Erro desconhecido';

      // Fornecer mensagens de erro mais específicas baseadas no status
      if (errorStatus === 400) {
        throw new Error(`Erro de validação: ${errorMessage}`);
      } else if (errorStatus === 401) {
        throw new Error('Não autorizado. Verifique se tem permissões de administrador.');
      } else if (errorStatus === 403) {
        throw new Error('Acesso negado. Privilégios de administrador necessários.');
      } else if (errorStatus === 404) {
        throw new Error('Função não encontrada. Verifique a configuração do sistema.');
      } else if (errorStatus === 500) {
        throw new Error(`Erro interno do servidor: ${errorMessage}`);
      } else if (errorStatus >= 500) {
        throw new Error(`Erro do servidor (${errorStatus}): ${errorMessage}`);
      } else {
        throw new Error(`Erro na chamada da função (${errorStatus}): ${errorMessage}`);
      }
    }

    // Verificar se data existe
    if (!response.data) {
      throw new Error('Nenhuma resposta recebida da função. Tente novamente.');
    }

    // Verificar se há erro na resposta (Edge Function pode retornar 200 mas com erro no body)
    if (response.data.error) {
      const errorMsg = typeof response.data.error === 'string' ? response.data.error : JSON.stringify(response.data.error);
      throw new Error(`Erro da função: ${errorMsg}`);
    }

    // Verificar se a resposta indica sucesso
    if (response.data.success === false || (!response.data.success && !response.data.user_id)) {
      const errorMsg = response.data.error || response.data.message || 'Operação não foi bem-sucedida';
      throw new Error(errorMsg);
    }

    // Validar estrutura da resposta de sucesso
    if (!response.data.user_id || !response.data.email || !response.data.name || !response.data.apartment_id) {
      throw new Error('Resposta da função está incompleta');
    }

    // Log da resposta completa para debug
    console.log('✅ [createResidentUserViaEdgeFunction] Success response:', response.data);

    return response.data;

  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Erro desconhecido ao criar utilizador');
  }
};
