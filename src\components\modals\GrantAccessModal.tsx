
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { AlertCircle, Eye, EyeOff, Copy, Download, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { 
  createResidentUser, 
  generateSecurePassword, 
  copyToClipboard, 
  generateCredentialsPDF, 
  sendCredentialsEmail,
  type CreateResidentUserParams,
  type CreateResidentUserResult
} from '@/utils/resident-access';

interface GrantAccessModalProps {
  open: boolean;
  onClose: () => void;
  apartmentId: string;
  onSuccess?: () => void;
}

const GrantAccessModal: React.FC<GrantAccessModalProps> = ({ open, onClose, apartmentId, onSuccess }) => {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [password, setPassword] = useState('');
  const [generatedPassword, setGeneratedPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [sending, setSending] = useState(false);
  const [sendToResident, setSendToResident] = useState(true);
  const [creating, setCreating] = useState(false);
  const [acceptedTerms, setAcceptedTerms] = useState(false);

  const generatePassword = () => {
    setGenerating(true);
    const newPassword = generateSecurePassword();
    setPassword(newPassword);
    setGeneratedPassword(newPassword);
    setGenerating(false);
  };

  const copyPassword = async () => {
    const copied = await copyToClipboard(password);
    if (copied) {
      toast.success('Senha copiada para a área de transferência!');
    } else {
      toast.error('Falha ao copiar a senha.');
    }
  };

  const downloadCredentials = async () => {
    try {
      setSending(true);
      await generateCredentialsPDF({ name, email, password, apartment: apartmentId });
      setSending(false);
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      toast.error('Erro ao gerar PDF.');
      setSending(false);
    }
  };

  const handleCreateUser = async () => {
    if (!acceptedTerms) {
      toast.error('Aceite os termos para continuar.');
      return;
    }

    if (!email || !password || !name || !apartmentId) {
      toast.error('Preencha todos os campos.');
      return;
    }

    setCreating(true);
    try {
      const params: CreateResidentUserParams = {
        email,
        password,
        name,
        apartment_id: apartmentId
      };

      const result: CreateResidentUserResult = await createResidentUser(params);

      if (result.success) {
        toast.success(`Usuário ${result.name} criado com sucesso!`);

        // Enviar email de credenciais
        try {
          setSending(true);
          const emailResult = await sendCredentialsEmail({ name, email, password, apartment: apartmentId }, sendToResident);
          setSending(false);

          if (emailResult === true) {
            toast.success('Email de credenciais enviado com sucesso!');
          } else if (emailResult === 'simulated') {
            toast.warning('Email de credenciais simulado (API KEY não configurada)!');
          } else {
            toast.error('Falha ao enviar email de credenciais.');
          }
        } catch (emailError) {
          console.error('Erro ao enviar email:', emailError);
          toast.error('Erro ao enviar email de credenciais.');
          setSending(false);
        }

        onSuccess?.();
        onClose();
      } else {
        toast.error(result.message || 'Erro ao criar usuário.');
      }
    } catch (error: any) {
      console.error('Erro ao criar usuário:', error);
      toast.error(error.message || 'Erro ao criar usuário.');
    } finally {
      setCreating(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Conceder Acesso ao Morador</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Nome
            </Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="email" className="text-right">
              Email
            </Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="password" className="text-right">
              Senha
            </Label>
            <div className="col-span-3 relative">
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full pr-10"
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer">
                {password && (
                  showPassword ? (
                    <EyeOff size={20} onClick={() => setShowPassword(false)} className="text-gray-500 hover:text-gray-700" />
                  ) : (
                    <Eye size={20} onClick={() => setShowPassword(true)} className="text-gray-500 hover:text-gray-700" />
                  )
                )}
              </div>
            </div>
          </div>

          {/* Password Actions */}
          {generatedPassword ? (
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right"></Label>
              <div className="col-span-3 flex space-x-2">
                <Button
                  type="button"
                  variant="secondary"
                  size="sm"
                  onClick={copyPassword}
                >
                  <Copy size={16} className="mr-2" />
                  Copiar
                </Button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right"></Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="col-span-3"
                onClick={generatePassword}
                disabled={generating}
              >
                {generating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Gerando...
                  </>
                ) : (
                  'Gerar Senha Segura'
                )}
              </Button>
            </div>
          )}

          {/* Send to Resident Switch */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="sendToResident" className="text-right">
              Enviar para o Morador
            </Label>
            <div className="col-span-3 flex items-center">
              <Switch
                id="sendToResident"
                checked={sendToResident}
                onCheckedChange={(checked) => setSendToResident(checked)}
              />
            </div>
          </div>

          {/* Download Credentials Button */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right"></Label>
            <Button
              type="button"
              variant="secondary"
              size="sm"
              className="col-span-3"
              onClick={downloadCredentials}
              disabled={sending}
            >
              {sending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Gerando PDF...
                </>
              ) : (
                <>
                  <Download size={16} className="mr-2" />
                  Gerar PDF
                </>
              )}
            </Button>
          </div>

          {/* Terms Acceptance */}
          <div className="grid grid-cols-4 items-start gap-4">
            <Label className="text-right"></Label>
            <div className="col-span-3 flex items-center space-x-2">
              <Checkbox
                id="terms"
                checked={acceptedTerms}
                onCheckedChange={(checked) => setAcceptedTerms(checked === true)}
              />
              <div className="text-sm text-gray-500 leading-relaxed">
                Eu concordo com os termos de serviço e política de privacidade.
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-2">
          <Button type="button" variant="ghost" onClick={onClose}>
            Cancelar
          </Button>
          <Button type="submit" onClick={handleCreateUser} disabled={creating}>
            {creating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Criando...
              </>
            ) : (
              'Criar Usuário'
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default GrantAccessModal;
