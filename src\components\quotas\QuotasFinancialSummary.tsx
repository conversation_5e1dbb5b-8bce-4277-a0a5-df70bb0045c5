import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DollarSign, Receipt, Calculator } from 'lucide-react';
import { cn } from '@/lib/utils';

interface QuotasFinancialSummaryProps {
  totalQuotaAmount: number;
  totalFinesAmount: number;
  combinedTotal: number;
  quotaCount: number;
  fineCount: number;
  isLoading?: boolean;
  className?: string;
}

const QuotasFinancialSummary: React.FC<QuotasFinancialSummaryProps> = ({
  totalQuotaAmount,
  totalFinesAmount,
  combinedTotal,
  quotaCount,
  fineCount,
  isLoading = false,
  className
}) => {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value).replace('AOA', 'Kz');
  };

  if (isLoading) {
    return (
      <div className={cn("grid grid-cols-1 md:grid-cols-3 gap-4 animate-pulse", className)}>
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/3 mt-2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className={cn("grid grid-cols-1 md:grid-cols-3 gap-4", className)}>
      {/* Combined Total */}
      <Card className="border-green-200 bg-green-50/50">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-green-700 flex items-center gap-2">
            <Calculator className="h-4 w-4" />
            Total Combinado
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-900">
            {formatCurrency(combinedTotal)}
          </div>
          <p className="text-xs text-green-600 mt-1">
            Quotas + Multas
          </p>
        </CardContent>
      </Card>

      {/* Total Quotas */}
      <Card className="border-blue-200 bg-blue-50/50">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-blue-700 flex items-center gap-2">
            <Receipt className="h-4 w-4" />
            Total de Quotas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-900">
            {formatCurrency(totalQuotaAmount)}
          </div>
          <p className="text-xs text-blue-600 mt-1">
            {quotaCount} quota{quotaCount !== 1 ? 's' : ''}
          </p>
        </CardContent>
      </Card>

      {/* Total Fines */}
      <Card className="border-orange-200 bg-orange-50/50">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-orange-700 flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Total de Multas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-orange-900">
            {formatCurrency(totalFinesAmount)}
          </div>
          <p className="text-xs text-orange-600 mt-1">
            {fineCount} multa{fineCount !== 1 ? 's' : ''}
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default QuotasFinancialSummary;
