/**
 * Test utility to verify tab badge counting logic
 * This file can be used for manual testing of the filtering logic
 */

import { Quota } from '@/types';

// Mock data for testing
const mockQuotas: Quota[] = [
  {
    id: '1',
    morador_id: 'morador1',
    mes: 3, // March
    ano: 2024,
    valor: 2000,
    status: 'Não Pago',
    multa: 1000,
    situacao: 'Não Regularizada',
    data_vencimento: '2024-03-15',
    moradores: { nome: '<PERSON>', apartamento: '1A' }
  },
  {
    id: '2',
    morador_id: 'morador1',
    mes: 4, // April
    ano: 2024,
    valor: 2000,
    status: 'Pago',
    multa: 500,
    situacao: 'Regularizada',
    data_vencimento: '2024-04-15',
    data_pagamento: '2024-04-10',
    moradores: { nome: '<PERSON>', apartamento: '1A' }
  },
  {
    id: '3',
    morador_id: 'morador2',
    mes: 3, // March
    ano: 2024,
    valor: 2000,
    status: 'Não Pago',
    multa: 0,
    situacao: 'Não Regularizada',
    data_vencimento: '2024-03-15',
    moradores: { nome: '<PERSON>', apartamento: '2B' }
  },
  {
    id: '4',
    morador_id: 'morador2',
    mes: 12, // December (current month for testing)
    ano: 2024,
    valor: 2000,
    status: 'Não Pago',
    multa: 0,
    situacao: 'Não Regularizada',
    data_vencimento: '2024-12-15',
    moradores: { nome: 'Maria Santos', apartamento: '2B' }
  }
];

// Test filtering functions
export const testTabBadgeLogic = () => {
  console.log('🧪 Testing Tab Badge Logic');
  console.log('📊 Mock Data:', mockQuotas.length, 'quotas');

  // Test 1: No filters applied
  console.log('\n📋 Test 1: No filters');
  const allQuotas = mockQuotas;
  console.log('- All quotas:', allQuotas.length);
  console.log('- Current month (Dec 2024):', allQuotas.filter(q => q.mes === 12 && q.ano === 2024).length);
  console.log('- Unpaid fines:', allQuotas.filter(q => q.multa > 0 && q.situacao !== 'Regularizada').length);
  console.log('- All fines:', allQuotas.filter(q => q.multa > 0).length);

  // Test 2: Filter by month (March)
  console.log('\n📋 Test 2: Filter by March 2024');
  const marchQuotas = mockQuotas.filter(q => q.mes === 3 && q.ano === 2024);
  console.log('- All quotas:', marchQuotas.length);
  console.log('- Current month (Dec 2024):', marchQuotas.filter(q => q.mes === 12 && q.ano === 2024).length);
  console.log('- Unpaid fines:', marchQuotas.filter(q => q.multa > 0 && q.situacao !== 'Regularizada').length);
  console.log('- All fines:', marchQuotas.filter(q => q.multa > 0).length);

  // Test 3: Filter by resident
  console.log('\n📋 Test 3: Filter by João Silva');
  const joaoQuotas = mockQuotas.filter(q => q.morador_id === 'morador1');
  console.log('- All quotas:', joaoQuotas.length);
  console.log('- Current month (Dec 2024):', joaoQuotas.filter(q => q.mes === 12 && q.ano === 2024).length);
  console.log('- Unpaid fines:', joaoQuotas.filter(q => q.multa > 0 && q.situacao !== 'Regularizada').length);
  console.log('- All fines:', joaoQuotas.filter(q => q.multa > 0).length);

  // Test 4: Filter by status (Não Pago)
  console.log('\n📋 Test 4: Filter by Não Pago status');
  const unpaidQuotas = mockQuotas.filter(q => q.status === 'Não Pago');
  console.log('- All quotas:', unpaidQuotas.length);
  console.log('- Current month (Dec 2024):', unpaidQuotas.filter(q => q.mes === 12 && q.ano === 2024).length);
  console.log('- Unpaid fines:', unpaidQuotas.filter(q => q.multa > 0 && q.situacao !== 'Regularizada').length);
  console.log('- All fines:', unpaidQuotas.filter(q => q.multa > 0).length);

  console.log('\n✅ Tab Badge Logic Test Complete');
};

// Expected results for validation:
// Test 1 (No filters): All=4, Current=1, Unpaid Fines=1, All Fines=2
// Test 2 (March 2024): All=2, Current=0, Unpaid Fines=1, All Fines=1  
// Test 3 (João Silva): All=2, Current=0, Unpaid Fines=1, All Fines=2
// Test 4 (Não Pago): All=3, Current=1, Unpaid Fines=1, All Fines=1

export { mockQuotas };
