
import React from 'react';
import DataTable from '@/components/tables/DataTable';
import { QuotaTabs } from '@/components/quotas/QuotaTabs';
import { Quota } from '@/types';

interface QuotasTableSectionProps {
  quotas: Quota[] | undefined;
  activeTab: string;
  onTabChange: (tab: string) => void;
  columns: any[];
  formatQuotasForTable: (quotas: Quota[]) => Quota[];
  getFilteredQuotasForBadges: (quotas: Quota[]) => Quota[]; // New prop for badge calculations
  isLoading: boolean;
  isDeletingQuota: boolean;
  isRegularizingFine: boolean;
  isGeneratingQuotas: boolean;
}

const QuotasTableSection: React.FC<QuotasTableSectionProps> = ({
  quotas,
  activeTab,
  onTabChange,
  columns,
  formatQuotasForTable,
  getFilteredQuotasForBadges,
  isLoading,
  isDeletingQuota,
  isRegularizingFine,
  isGeneratingQuotas
}) => {
  // Get filtered quotas for badge calculations (without tab-specific filtering)
  const filteredQuotasForBadges = getFilteredQuotasForBadges(quotas || []);

  return (
    <div className="mt-4 animate-enter">
      <QuotaTabs
        quotas={filteredQuotasForBadges}
        activeTab={activeTab}
        onTabChange={onTabChange}
      >
        <DataTable
          columns={columns}
          data={formatQuotasForTable(quotas || [])}
          enableSearch={false}
          enablePagination={true}
          isLoading={isLoading || isDeletingQuota || isRegularizingFine || isGeneratingQuotas}
        />
      </QuotaTabs>
    </div>
  );
};

export default QuotasTableSection;
