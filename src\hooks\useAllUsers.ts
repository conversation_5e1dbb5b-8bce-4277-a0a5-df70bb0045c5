
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  banned: boolean;
  created_at: string;
  status?: string;
}

const getAllUsers = async (): Promise<User[]> => {
  const { data, error } = await supabase.rpc('get_all_users');
  
  if (error) {
    throw new Error(error.message);
  }
  
  return data || [];
};

export const useAllUsers = () => {
  return useQuery({
    queryKey: ['all-users'],
    queryFn: getAllUsers,
    staleTime: 5 * 60 * 1000,
  });
};
